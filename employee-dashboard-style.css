/* Employee Dashboard Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    color: #333;
    padding: 1rem 2rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.breadcrumb {
    font-size: 0.9rem;
    color: #666;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.current-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1rem;
}

.user-menu {
    position: relative;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.user-info:hover {
    background: rgba(102, 126, 234, 0.1);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #667eea;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
    color: #333;
}

.user-role {
    font-size: 0.75rem;
    color: #666;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    padding: 0.5rem 0;
    display: none;
    z-index: 1001;
}

.user-dropdown.show {
    display: block;
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.user-dropdown a:hover {
    background: #f8f9fa;
}

.user-dropdown a.logout-btn:hover {
    background: #fee;
    color: #dc3545;
}

/* Main Container */
.main-container {
    margin-top: 70px;
    min-height: calc(100vh - 70px);
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

/* Welcome Section */
.welcome-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    color: #333;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.welcome-content h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
    color: #333;
}

.welcome-content p {
    color: #666;
    font-size: 1.1rem;
}

.attendance-status {
    display: flex;
    align-items: center;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.status-indicator.not-checked-in {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.status-indicator.checked-in {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.status-indicator.checked-out {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* Attendance Actions */
.attendance-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.action-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    text-align: center;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.action-card.disabled {
    opacity: 0.6;
    pointer-events: none;
}

.action-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1.5rem;
}

.check-in .action-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.check-out .action-icon {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.action-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.action-content p {
    color: #666;
    margin-bottom: 1.5rem;
}

.action-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto;
}

.action-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.check-out .action-btn {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.check-out .action-btn:hover:not(:disabled) {
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
}

/* Today's Summary */
.today-summary {
    margin-bottom: 2rem;
}

.summary-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.summary-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.summary-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
}

.summary-content {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    background: rgba(102, 126, 234, 0.05);
    transition: all 0.3s ease;
}

.summary-item:hover {
    background: rgba(102, 126, 234, 0.1);
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.summary-details {
    display: flex;
    flex-direction: column;
}

.summary-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.summary-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

/* Weekly Overview */
.weekly-overview {
    margin-bottom: 2rem;
}

.overview-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.card-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
}

.weekly-stats {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    background: rgba(102, 126, 234, 0.05);
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(102, 126, 234, 0.1);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.stat-icon.success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.stat-icon.warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.stat-icon.danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.stat-icon.info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.stat-content h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.25rem;
}

.stat-content p {
    font-size: 0.9rem;
    color: #666;
}

/* Recent Activity */
.recent-activity {
    margin-bottom: 2rem;
}

.activity-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.activity-list {
    padding: 1.5rem;
}

.activity-item {
    display: grid;
    grid-template-columns: 120px 1fr auto;
    gap: 1.5rem;
    align-items: center;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 1rem;
    background: rgba(102, 126, 234, 0.05);
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: rgba(102, 126, 234, 0.1);
}

.activity-item:last-child {
    margin-bottom: 0;
}

.activity-date {
    display: flex;
    flex-direction: column;
    text-align: center;
}

.activity-date .day {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.activity-date .date {
    font-size: 0.8rem;
    color: #666;
}

.activity-details {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.activity-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.activity-duration {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
}

.activity-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.activity-status.success {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.activity-status.warning {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 20px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.modal-header {
    padding: 2rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    text-align: center;
}

.modal-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.modal-header i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.modal-body {
    padding: 2rem;
    text-align: center;
}

.modal-body p {
    margin-bottom: 1rem;
    color: #666;
    line-height: 1.6;
}

.modal-actions {
    padding: 1rem 2rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .welcome-section {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .attendance-actions {
        grid-template-columns: 1fr;
    }
    
    .summary-content {
        grid-template-columns: 1fr;
    }
    
    .weekly-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .activity-item {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .activity-details {
        justify-content: center;
        flex-wrap: wrap;
    }
}
