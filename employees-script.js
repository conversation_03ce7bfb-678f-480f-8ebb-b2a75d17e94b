// Employees Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeEmployeesPage();
    loadEmployees();
    setupEventListeners();
});

// Sample employees data
const employeesData = [
    {
        id: 1,
        firstName: 'أحمد',
        lastName: 'محمد',
        email: '<EMAIL>',
        phone: '+966501234567',
        department: 'it',
        position: 'مطور برمجيات',
        hireDate: '2023-01-15',
        status: 'active',
        salary: 8000,
        avatar: 'أم'
    },
    {
        id: 2,
        firstName: 'فاطمة',
        lastName: 'علي',
        email: '<EMAIL>',
        phone: '+966507654321',
        department: 'hr',
        position: 'مختصة موارد بشرية',
        hireDate: '2022-08-20',
        status: 'active',
        salary: 7500,
        avatar: 'فع'
    },
    {
        id: 3,
        firstName: 'محمد',
        lastName: 'خالد',
        email: '<EMAIL>',
        phone: '+966509876543',
        department: 'finance',
        position: 'محاسب',
        hireDate: '2023-03-10',
        status: 'on-leave',
        salary: 6500,
        avatar: 'مخ'
    },
    {
        id: 4,
        firstName: 'سارة',
        lastName: 'أحمد',
        email: '<EMAIL>',
        phone: '+966502468135',
        department: 'marketing',
        position: 'مختصة تسويق',
        hireDate: '2023-05-01',
        status: 'active',
        salary: 7000,
        avatar: 'سأ'
    },
    {
        id: 5,
        firstName: 'عبدالله',
        lastName: 'محمود',
        email: '<EMAIL>',
        phone: '+966503691472',
        department: 'sales',
        position: 'مندوب مبيعات',
        hireDate: '2022-12-15',
        status: 'inactive',
        salary: 5500,
        avatar: 'عم'
    }
];

let currentPage = 1;
const itemsPerPage = 10;
let filteredEmployees = [...employeesData];

// Initialize employees page
function initializeEmployeesPage() {
    updateEmployeesStats();
    setupPagination();
}

// Load and display employees
function loadEmployees() {
    const tbody = document.getElementById('employees-tbody');
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const employeesToShow = filteredEmployees.slice(startIndex, endIndex);
    
    tbody.innerHTML = '';
    
    employeesToShow.forEach(employee => {
        const row = createEmployeeRow(employee);
        tbody.appendChild(row);
    });
    
    updatePaginationInfo();
}

// Create employee table row
function createEmployeeRow(employee) {
    const row = document.createElement('tr');
    
    const departmentNames = {
        'it': 'تقنية المعلومات',
        'hr': 'الموارد البشرية',
        'finance': 'المالية',
        'marketing': 'التسويق',
        'sales': 'المبيعات'
    };
    
    const statusNames = {
        'active': 'نشط',
        'inactive': 'غير نشط',
        'on-leave': 'في إجازة'
    };
    
    row.innerHTML = `
        <td>
            <input type="checkbox" class="employee-checkbox" data-id="${employee.id}">
        </td>
        <td>
            <div class="employee-info">
                <div class="employee-avatar">${employee.avatar}</div>
                <div class="employee-details">
                    <div class="employee-name">${employee.firstName} ${employee.lastName}</div>
                    <div class="employee-email">${employee.email}</div>
                </div>
            </div>
        </td>
        <td>${departmentNames[employee.department] || employee.department}</td>
        <td>${employee.position}</td>
        <td>${formatDate(employee.hireDate)}</td>
        <td>
            <span class="status-badge ${employee.status}">${statusNames[employee.status]}</span>
        </td>
        <td>
            <span class="salary-amount">${employee.salary.toLocaleString()} ر.س</span>
        </td>
        <td>
            <div class="actions-buttons">
                <button class="action-btn-small view" onclick="viewEmployee(${employee.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn-small edit" onclick="editEmployee(${employee.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn-small delete" onclick="deleteEmployee(${employee.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </td>
    `;
    
    return row;
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('employee-search');
    searchInput.addEventListener('input', function() {
        filterEmployees();
    });
    
    // Department filter
    const departmentFilter = document.getElementById('department-filter');
    departmentFilter.addEventListener('change', function() {
        filterEmployees();
    });
    
    // Status filter
    const statusFilter = document.getElementById('status-filter');
    statusFilter.addEventListener('change', function() {
        filterEmployees();
    });
    
    // Select all checkbox
    const selectAllCheckbox = document.getElementById('select-all');
    selectAllCheckbox.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.employee-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // Pagination buttons
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');
    
    prevBtn.addEventListener('click', function() {
        if (currentPage > 1) {
            currentPage--;
            loadEmployees();
            updatePaginationButtons();
        }
    });
    
    nextBtn.addEventListener('click', function() {
        const totalPages = Math.ceil(filteredEmployees.length / itemsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            loadEmployees();
            updatePaginationButtons();
        }
    });
    
    // Add employee form
    const addEmployeeForm = document.getElementById('add-employee-form');
    addEmployeeForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleAddEmployee(e);
    });
}

// Filter employees
function filterEmployees() {
    const searchTerm = document.getElementById('employee-search').value.toLowerCase();
    const departmentFilter = document.getElementById('department-filter').value;
    const statusFilter = document.getElementById('status-filter').value;
    
    filteredEmployees = employeesData.filter(employee => {
        const matchesSearch = 
            employee.firstName.toLowerCase().includes(searchTerm) ||
            employee.lastName.toLowerCase().includes(searchTerm) ||
            employee.email.toLowerCase().includes(searchTerm) ||
            employee.position.toLowerCase().includes(searchTerm);
        
        const matchesDepartment = !departmentFilter || employee.department === departmentFilter;
        const matchesStatus = !statusFilter || employee.status === statusFilter;
        
        return matchesSearch && matchesDepartment && matchesStatus;
    });
    
    currentPage = 1;
    loadEmployees();
    updatePaginationButtons();
    updateEmployeesStats();
}

// Update employees statistics
function updateEmployeesStats() {
    const stats = {
        total: filteredEmployees.length,
        active: filteredEmployees.filter(emp => emp.status === 'active').length,
        inactive: filteredEmployees.filter(emp => emp.status === 'inactive').length,
        onLeave: filteredEmployees.filter(emp => emp.status === 'on-leave').length
    };
    
    const statNumbers = document.querySelectorAll('.stat-number');
    if (statNumbers.length >= 4) {
        statNumbers[0].textContent = stats.total;
        statNumbers[1].textContent = stats.active;
        statNumbers[2].textContent = stats.inactive;
        statNumbers[3].textContent = stats.onLeave;
    }
}

// Setup pagination
function setupPagination() {
    updatePaginationButtons();
}

// Update pagination info
function updatePaginationInfo() {
    const totalPages = Math.ceil(filteredEmployees.length / itemsPerPage);
    document.getElementById('current-page').textContent = currentPage;
    document.getElementById('total-pages').textContent = totalPages;
}

// Update pagination buttons
function updatePaginationButtons() {
    const totalPages = Math.ceil(filteredEmployees.length / itemsPerPage);
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');
    
    prevBtn.disabled = currentPage === 1;
    nextBtn.disabled = currentPage === totalPages || totalPages === 0;
}

// Handle add employee
function handleAddEmployee(e) {
    const formData = new FormData(e.target);
    const employeeData = Object.fromEntries(formData);
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'جاري الإضافة...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Generate new employee
        const newEmployee = {
            id: employeesData.length + 1,
            firstName: employeeData.firstName,
            lastName: employeeData.lastName,
            email: employeeData.email,
            phone: employeeData.phone,
            department: employeeData.department,
            position: employeeData.position,
            hireDate: employeeData.hireDate,
            status: 'active',
            salary: parseFloat(employeeData.salary),
            avatar: employeeData.firstName.charAt(0) + employeeData.lastName.charAt(0)
        };
        
        // Add to data
        employeesData.push(newEmployee);
        filteredEmployees = [...employeesData];
        
        // Reload table
        loadEmployees();
        updateEmployeesStats();
        
        // Show success message
        showNotification('تم إضافة الموظف بنجاح!', 'success');
        closeModal('add-employee');
        
        // Reset button
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

// View employee
function viewEmployee(id) {
    const employee = employeesData.find(emp => emp.id === id);
    if (employee) {
        alert(`عرض تفاصيل الموظف: ${employee.firstName} ${employee.lastName}`);
        // Here you would typically open a detailed view modal
    }
}

// Edit employee
function editEmployee(id) {
    const employee = employeesData.find(emp => emp.id === id);
    if (employee) {
        // Populate edit form with employee data
        populateEditForm(employee);
        openModal('edit-employee');
    }
}

// Populate edit form
function populateEditForm(employee) {
    const form = document.getElementById('edit-employee-form');
    // This would populate the form fields with employee data
    console.log('Editing employee:', employee);
}

// Delete employee
function deleteEmployee(id) {
    const employee = employeesData.find(emp => emp.id === id);
    if (employee && confirm(`هل أنت متأكد من حذف الموظف ${employee.firstName} ${employee.lastName}؟`)) {
        // Remove from data
        const index = employeesData.findIndex(emp => emp.id === id);
        if (index > -1) {
            employeesData.splice(index, 1);
            filteredEmployees = [...employeesData];
            
            // Reload table
            loadEmployees();
            updateEmployeesStats();
            
            showNotification('تم حذف الموظف بنجاح!', 'success');
        }
    }
}

// Export employees
function exportEmployees() {
    // Get selected employees or all if none selected
    const selectedCheckboxes = document.querySelectorAll('.employee-checkbox:checked');
    const selectedIds = Array.from(selectedCheckboxes).map(cb => parseInt(cb.dataset.id));
    
    const employeesToExport = selectedIds.length > 0 
        ? filteredEmployees.filter(emp => selectedIds.includes(emp.id))
        : filteredEmployees;
    
    if (employeesToExport.length === 0) {
        showNotification('لا توجد بيانات للتصدير', 'error');
        return;
    }
    
    // Create CSV content
    const headers = ['الاسم الأول', 'الاسم الأخير', 'البريد الإلكتروني', 'الهاتف', 'القسم', 'المنصب', 'تاريخ التوظيف', 'الحالة', 'الراتب'];
    const csvContent = [
        headers.join(','),
        ...employeesToExport.map(emp => [
            emp.firstName,
            emp.lastName,
            emp.email,
            emp.phone,
            emp.department,
            emp.position,
            emp.hireDate,
            emp.status,
            emp.salary
        ].join(','))
    ].join('\n');
    
    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'employees.csv';
    link.click();
    
    showNotification(`تم تصدير ${employeesToExport.length} موظف بنجاح!`, 'success');
}

console.log('صفحة إدارة الموظفين - موقوت');
