<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور | موقوت</title>
    <link rel="icon" type="image/x-icon" href="https://cdn.mawqoot.com/static/images/favicon.ico">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="login-style.css">
</head>
<body>
    <!-- Main Container -->
    <div class="login-container">
        <!-- Left Side - Reset Form -->
        <div class="login-form-section">
            <!-- Logo -->
            <div class="logo-container">
                <img src="https://cdn.mawqoot.com/static/images/logo/logo-ar.png" alt="موقوت" class="logo">
            </div>

            <!-- Reset Form -->
            <div class="login-form">
                <h1>إعادة تعيين كلمة المرور</h1>
                <p class="reset-description">أدخل بريدك الإلكتروني وسنرسل لك رابط لإعادة تعيين كلمة المرور</p>
                
                <form id="reset-form">
                    <div class="form-group">
                        <label for="reset-email">البريد الإلكتروني</label>
                        <input type="email" id="reset-email" name="email" required>
                    </div>
                    
                    <button type="submit" class="login-btn">
                        <span class="btn-text">إرسال رابط الإعادة</span>
                        <div class="loading-spinner" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </button>
                </form>
                
                <div class="register-link">
                    <p>تذكرت كلمة المرور؟ <a href="login.html">تسجيل الدخول</a></p>
                </div>
            </div>
        </div>

        <!-- Right Side - Background Image -->
        <div class="login-background">
            <div class="background-overlay">
                <div class="background-content">
                    <h2>استعادة الوصول لحسابك</h2>
                    <p>لا تقلق، يحدث هذا للجميع. سنساعدك في استعادة الوصول لحسابك بسهولة</p>
                    <div class="features-list">
                        <div class="feature-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>عملية آمنة ومشفرة</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-clock"></i>
                            <span>استجابة سريعة</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-user-lock"></i>
                            <span>حماية بياناتك</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    <div id="success-message" class="success-container" style="display: none;">
        <div class="success-content">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h2>تم إرسال الرابط بنجاح!</h2>
            <p>تحقق من بريدك الإلكتروني واتبع التعليمات لإعادة تعيين كلمة المرور</p>
            <div class="success-actions">
                <a href="login.html" class="btn-primary">العودة لتسجيل الدخول</a>
                <button onclick="resendEmail()" class="btn-secondary">إعادة الإرسال</button>
            </div>
        </div>
    </div>

    <style>
        .reset-description {
            color: #666;
            margin-bottom: 2rem;
            text-align: center;
            line-height: 1.6;
        }

        .success-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        }

        .success-content {
            text-align: center;
            max-width: 500px;
            padding: 3rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 1.5rem;
        }

        .success-content h2 {
            color: #333;
            margin-bottom: 1rem;
        }

        .success-content p {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .success-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .success-actions .btn-primary,
        .success-actions .btn-secondary {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
        }

        .success-actions .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .success-actions .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .success-actions .btn-secondary {
            background: #e9ecef;
            color: #333;
        }

        .success-actions .btn-secondary:hover {
            background: #dee2e6;
        }

        @media (max-width: 768px) {
            .success-content {
                margin: 1rem;
                padding: 2rem;
            }

            .success-actions {
                flex-direction: column;
            }

            .success-actions .btn-primary,
            .success-actions .btn-secondary {
                width: 100%;
            }
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resetForm = document.getElementById('reset-form');
            const emailInput = document.getElementById('reset-email');
            const resetBtn = document.querySelector('.login-btn');
            const successMessage = document.getElementById('success-message');

            function validateEmail(email) {
                const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return re.test(email);
            }

            function showError(input, message) {
                const formGroup = input.parentElement;
                formGroup.classList.add('error');
                
                let errorElement = formGroup.querySelector('.error-message');
                if (!errorElement) {
                    errorElement = document.createElement('div');
                    errorElement.className = 'error-message';
                    formGroup.appendChild(errorElement);
                }
                errorElement.textContent = message;
            }

            function clearError(input) {
                const formGroup = input.parentElement;
                formGroup.classList.remove('error');
                
                const errorElement = formGroup.querySelector('.error-message');
                if (errorElement) {
                    errorElement.style.display = 'none';
                }
            }

            emailInput.addEventListener('input', function() {
                clearError(this);
            });

            resetForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = emailInput.value.trim();
                
                if (!email) {
                    showError(emailInput, 'البريد الإلكتروني مطلوب');
                    return;
                }
                
                if (!validateEmail(email)) {
                    showError(emailInput, 'يرجى إدخال بريد إلكتروني صحيح');
                    return;
                }

                // Show loading state
                resetBtn.classList.add('loading');
                resetBtn.disabled = true;

                // Simulate sending reset email
                setTimeout(() => {
                    console.log('Reset email sent to:', email);
                    successMessage.style.display = 'flex';
                    resetBtn.classList.remove('loading');
                    resetBtn.disabled = false;
                }, 2000);
            });

            window.resendEmail = function() {
                const email = emailInput.value.trim();
                console.log('Resending email to:', email);
                
                // Show notification
                const notification = document.createElement('div');
                notification.textContent = 'تم إعادة إرسال الرابط بنجاح!';
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #d4edda;
                    color: #155724;
                    border: 1px solid #c3e6cb;
                    border-radius: 8px;
                    padding: 1rem;
                    z-index: 10001;
                    animation: slideInRight 0.3s ease;
                `;
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.remove();
                }, 3000);
            };
        });
    </script>
</body>
</html>
