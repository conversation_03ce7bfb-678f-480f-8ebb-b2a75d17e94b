<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموظفين | KHALAiFAT Live Location</title>
    <link rel="icon" type="image/x-icon" href="https://cdn.mawqoot.com/static/images/favicon.ico">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="dashboard-style.css">
    <link rel="stylesheet" href="employees-style.css">
</head>

<body>
    <!-- Authentication Check Script -->
    <script src="auth-system.js"></script>
    <script>
        // Check authentication and permissions
        if (!authSystem.isLoggedIn()) {
            window.location.href = 'login.html';
        } else {
            const currentUser = authSystem.getCurrentUser();
            // Only admin and supervisor can access employees page
            if (!authSystem.hasPermission('view_employees')) {
                alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
                window.location.href = authSystem.getDashboardUrl();
            }
        }
    </script>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-map-marker-alt" style="color: #667eea; font-size: 2rem; margin-bottom: 0.5rem;"></i>
                <span style="font-size: 1.5rem; font-weight: 700; color: #333;">KHALAiFAT</span>
            </div>
            <button class="sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul>
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="employees.html" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>الموظفين</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="attendance.html" class="nav-link">
                        <i class="fas fa-clock"></i>
                        <span>الحضور والانصراف</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="leaves.html" class="nav-link">
                        <i class="fas fa-calendar-times"></i>
                        <span>الإجازات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="violations.html" class="nav-link">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>المخالفات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="reports.html" class="nav-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>التقارير</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="payroll.html" class="nav-link">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>الرواتب</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="settings.html" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name">أحمد محمد</span>
                    <span class="user-role">مدير النظام</span>
                </div>
            </div>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        <!-- Top Header -->
        <header class="top-header">
            <div class="header-left">
                <button class="mobile-menu-btn" id="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>إدارة الموظفين</h1>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="btn-primary" onclick="openModal('add-employee')">
                        <i class="fas fa-plus"></i>
                        <span>إضافة موظف</span>
                    </button>
                    <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Employees Content -->
        <div class="employees-content">
            <!-- Filters and Search -->
            <div class="filters-section">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="employee-search" placeholder="البحث عن موظف...">
                </div>

                <div class="filters">
                    <select id="department-filter">
                        <option value="">جميع الأقسام</option>
                        <option value="it">تقنية المعلومات</option>
                        <option value="hr">الموارد البشرية</option>
                        <option value="finance">المالية</option>
                        <option value="marketing">التسويق</option>
                        <option value="sales">المبيعات</option>
                    </select>

                    <select id="status-filter">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="on-leave">في إجازة</option>
                    </select>

                    <button class="filter-btn" onclick="exportEmployees()">
                        <i class="fas fa-download"></i>
                        <span>تصدير</span>
                    </button>
                </div>
            </div>

            <!-- Employees Stats -->
            <div class="employees-stats">
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-details">
                        <span class="stat-number">156</span>
                        <span class="stat-label">إجمالي الموظفين</span>
                    </div>
                </div>

                <div class="stat-item">
                    <div class="stat-icon active">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-details">
                        <span class="stat-number">142</span>
                        <span class="stat-label">الموظفين النشطين</span>
                    </div>
                </div>

                <div class="stat-item">
                    <div class="stat-icon inactive">
                        <i class="fas fa-user-times"></i>
                    </div>
                    <div class="stat-details">
                        <span class="stat-number">8</span>
                        <span class="stat-label">غير نشطين</span>
                    </div>
                </div>

                <div class="stat-item">
                    <div class="stat-icon leave">
                        <i class="fas fa-calendar-times"></i>
                    </div>
                    <div class="stat-details">
                        <span class="stat-number">6</span>
                        <span class="stat-label">في إجازة</span>
                    </div>
                </div>
            </div>

            <!-- Employees Table -->
            <div class="employees-table-container">
                <table class="employees-table" id="employees-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="select-all">
                            </th>
                            <th>الموظف</th>
                            <th>القسم</th>
                            <th>المنصب</th>
                            <th>تاريخ التوظيف</th>
                            <th>الحالة</th>
                            <th>الراتب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="employees-tbody">
                        <!-- Employee rows will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="pagination">
                <button class="pagination-btn" id="prev-page" disabled>
                    <i class="fas fa-chevron-right"></i>
                </button>
                <div class="pagination-info">
                    <span>صفحة <span id="current-page">1</span> من <span id="total-pages">10</span></span>
                </div>
                <button class="pagination-btn" id="next-page">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Add Employee Modal -->
    <div id="add-employee-modal" class="modal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3>إضافة موظف جديد</h3>
                <span class="close-modal" onclick="closeModal('add-employee')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="add-employee-form">
                    <div class="form-section">
                        <h4>المعلومات الشخصية</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>الاسم الأول *</label>
                                <input type="text" name="firstName" required>
                            </div>
                            <div class="form-group">
                                <label>الاسم الأخير *</label>
                                <input type="text" name="lastName" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>البريد الإلكتروني *</label>
                                <input type="email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label>رقم الهاتف *</label>
                                <input type="tel" name="phone" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>تاريخ الميلاد</label>
                                <input type="date" name="birthDate">
                            </div>
                            <div class="form-group">
                                <label>الجنسية</label>
                                <input type="text" name="nationality">
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h4>معلومات العمل</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>القسم *</label>
                                <select name="department" required>
                                    <option value="">اختر القسم</option>
                                    <option value="it">تقنية المعلومات</option>
                                    <option value="hr">الموارد البشرية</option>
                                    <option value="finance">المالية</option>
                                    <option value="marketing">التسويق</option>
                                    <option value="sales">المبيعات</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>المنصب *</label>
                                <input type="text" name="position" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>تاريخ التوظيف *</label>
                                <input type="date" name="hireDate" required>
                            </div>
                            <div class="form-group">
                                <label>الراتب الأساسي *</label>
                                <input type="number" name="salary" required min="0" step="0.01">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>المدير المباشر</label>
                                <select name="manager">
                                    <option value="">اختر المدير</option>
                                    <option value="1">أحمد محمد</option>
                                    <option value="2">فاطمة علي</option>
                                    <option value="3">محمد خالد</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>نوع العقد</label>
                                <select name="contractType">
                                    <option value="full-time">دوام كامل</option>
                                    <option value="part-time">دوام جزئي</option>
                                    <option value="contract">عقد مؤقت</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal('add-employee')">إلغاء</button>
                        <button type="submit" class="btn-primary">إضافة الموظف</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Employee Modal -->
    <div id="edit-employee-modal" class="modal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3>تعديل بيانات الموظف</h3>
                <span class="close-modal" onclick="closeModal('edit-employee')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="edit-employee-form">
                    <!-- Same form structure as add employee -->
                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal('edit-employee')">إلغاء</button>
                        <button type="submit" class="btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Custom JavaScript -->
    <script src="dashboard-script.js"></script>
    <script src="employees-script.js"></script>
</body>

</html>