/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
    color: #666;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    display: inline-block;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    padding: 12px 24px;
    border: 2px solid #667eea;
    border-radius: 8px;
    text-decoration: none;
    display: inline-block;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

/* Header */
.header {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    height: 40px;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #667eea;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text h1 {
    color: #333;
    margin-bottom: 1.5rem;
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image img {
    width: 100%;
    height: auto;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    color: #333;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    max-width: 800px;
    margin: 0 auto;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: white;
}

.feature-block {
    margin-bottom: 6rem;
}

.feature-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.feature-block.reverse .feature-content {
    direction: ltr;
}

.feature-block.reverse .feature-text {
    direction: rtl;
}

.feature-text h3 {
    color: #333;
    margin-bottom: 1.5rem;
}

.feature-lists {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin: 2rem 0;
}

.feature-list h4 {
    color: #667eea;
    margin-bottom: 1rem;
}

.feature-list ul {
    list-style: none;
}

.feature-list li {
    padding: 0.5rem 0;
    position: relative;
    padding-right: 1.5rem;
}

.feature-list li:before {
    content: "✓";
    position: absolute;
    right: 0;
    color: #667eea;
    font-weight: bold;
}

.feature-image img {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Video Section */
.video-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.video-container {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
}

.video-thumbnail {
    position: relative;
    cursor: pointer;
}

.video-thumbnail img {
    width: 100%;
    border-radius: 12px;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(102, 126, 234, 0.9);
    color: white;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.play-button:hover {
    background: #667eea;
    transform: translate(-50%, -50%) scale(1.1);
}

/* How Section */
.how-section {
    padding: 80px 0;
    background: white;
}

.steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.step {
    text-align: center;
    padding: 2rem;
    border-radius: 12px;
    background: #f8f9fa;
    transition: transform 0.3s ease;
}

.step:hover {
    transform: translateY(-5px);
}

.step-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1.5rem;
}

.cta-button {
    text-align: center;
}

/* App Section */
.app-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.app-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.app-text h2 {
    color: white;
    margin-bottom: 1.5rem;
}

.app-text p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
}

.app-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.app-btn img {
    height: 50px;
    transition: transform 0.3s ease;
}

.app-btn:hover img {
    transform: scale(1.05);
}

.app-image img {
    width: 100%;
    height: auto;
}

/* Why Section */
.why-section {
    padding: 80px 0;
    background: white;
}

.why-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.why-feature {
    text-align: center;
    padding: 2rem;
}

.why-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
}

/* Pricing Section */
.pricing-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.pricing-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.toggle-label {
    font-weight: 600;
    color: #333;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked+.slider {
    background-color: #667eea;
}

input:checked+.slider:before {
    transform: translateX(26px);
}

.pricing-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.pricing-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-5px);
}

.pricing-card h3 {
    color: #333;
    margin-bottom: 0.5rem;
}

.employee-count {
    color: #666;
    margin-bottom: 2rem;
}

.price {
    margin-bottom: 1rem;
}

.price span {
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
}

.price small {
    display: block;
    color: #666;
    margin-top: 0.5rem;
}

.yearly-note {
    color: #28a745;
    font-weight: 600;
    margin-bottom: 2rem;
}

.custom-price {
    font-size: 1.5rem;
    color: #667eea;
    font-weight: bold;
}

.contact-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.price-calculator {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    max-width: 600px;
    margin: 0 auto;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.calculator-input {
    margin-bottom: 2rem;
}

.calculator-input label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.calculator-input input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.calculator-input input:focus {
    outline: none;
    border-color: #667eea;
}

.calculator-results {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.result-item {
    text-align: center;
}

.result-item h4 {
    color: #333;
    margin-bottom: 1rem;
}

.result-price span {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.result-price small {
    display: block;
    color: #666;
    margin-top: 0.5rem;
}

/* Partners Section */
.partners-section {
    padding: 60px 0;
    background: white;
    text-align: center;
}

.partners-section h2 {
    margin-bottom: 3rem;
    color: #333;
}

.partners-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.partners-logos img {
    height: 60px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.partners-logos img:hover {
    opacity: 1;
}

/* Testimonials Section */
.testimonials-section {
    padding: 80px 0;
    background: #f8f9fa;
    text-align: center;
}

.testimonials {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.testimonial {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.testimonial p {
    font-style: italic;
    margin-bottom: 1.5rem;
    color: #333;
}

.testimonial-author h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.testimonial-author span {
    color: #666;
    font-size: 0.9rem;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: white;
}

.faq-items {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 1rem;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
    cursor: pointer;
    transition: color 0.3s ease;
}

.faq-question:hover {
    color: #667eea;
}

.faq-question h3 {
    margin: 0;
    font-size: 1.1rem;
}

.faq-question i {
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 0 1.5rem 0;
    display: none;
}

.faq-item.active .faq-answer {
    display: block;
}

.faq-answer ul {
    margin: 1rem 0;
    padding-right: 1.5rem;
}

.faq-answer li {
    margin-bottom: 0.5rem;
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
    background: #f8f9fa;
    text-align: center;
}

.contact-section h2 {
    margin-bottom: 3rem;
    color: #333;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    max-width: 800px;
    margin: 0 auto;
}

.contact-item h3 {
    color: #333;
    margin-bottom: 1rem;
}

.contact-item p {
    color: #666;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    margin-bottom: 3rem;
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
}

.footer-logo {
    height: 40px;
    margin-bottom: 1rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: white;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #667eea;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.footer-column h3 {
    color: white;
    margin-bottom: 1rem;
}

.footer-column ul {
    list-style: none;
}

.footer-column li {
    margin-bottom: 0.5rem;
}

.footer-column a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-column a:hover {
    color: white;
}

.app-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.app-links img {
    height: 40px;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: white;
        flex-direction: column;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .nav-menu.active {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-buttons {
        justify-content: center;
    }

    .feature-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .feature-block.reverse .feature-content {
        direction: rtl;
    }

    .feature-lists {
        grid-template-columns: 1fr;
    }

    .app-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }

    .pricing-cards {
        grid-template-columns: 1fr;
    }

    .calculator-results {
        grid-template-columns: 1fr;
    }

    .contact-info {
        grid-template-columns: 1fr;
    }

    .partners-logos {
        gap: 1.5rem;
    }

    .partners-logos img {
        height: 40px;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .container {
        padding: 0 15px;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 100px 0 60px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-primary,
    .btn-outline {
        width: 100%;
        text-align: center;
        max-width: 300px;
    }

    .footer-links {
        grid-template-columns: 1fr;
    }

    .app-links {
        flex-direction: row;
        justify-content: center;
    }

    .steps {
        grid-template-columns: 1fr;
    }

    .why-features {
        grid-template-columns: 1fr;
    }

    .testimonials {
        grid-template-columns: 1fr;
    }

    h1 {
        font-size: 1.8rem;
    }

    .hero-text p {
        font-size: 1rem;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.loading {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Button Loading State */
.btn-primary.loading,
.btn-outline.loading {
    position: relative;
    color: transparent;
}

.btn-primary.loading::after,
.btn-outline.loading::after {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.btn-outline.loading::after {
    border-top-color: #667eea;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Tooltip Styles */
.tooltip {
    position: absolute;
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.9rem;
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
    animation: fadeIn 0.3s ease forwards;
}

.tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border: 5px solid transparent;
    border-top-color: #333;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

/* Enhanced Hover Effects */
.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
}

.testimonial:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.step:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.1);
}

/* Gradient Text Effect */
.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Floating Animation */
@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-10px);
    }
}

.hero-image img {
    animation: float 3s ease-in-out infinite;
}

/* Pulse Animation for CTA Buttons */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

.btn-primary:hover {
    animation: pulse 2s infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #667eea;
}