<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - KHALAiFAT Live Location</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="admin-dashboard-style.css">
    
    <script>
        // Check authentication before page loads
        if (!window.authSystem) {
            // Load auth system first
            const script = document.createElement('script');
            script.src = 'auth-system.js';
            script.onload = function() {
                checkAuth();
            };
            document.head.appendChild(script);
        } else {
            checkAuth();
        }
        
        function checkAuth() {
            if (!authSystem.isLoggedIn()) {
                window.location.href = 'login.html';
            } else if (!authSystem.canManageUsers()) {
                alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
                window.location.href = authSystem.getDashboardUrl();
            }
        }
    </script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-right">
                <div class="logo">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>KHALAiFAT</span>
                </div>
                <div class="breadcrumb">
                    <a href="admin-dashboard.html">لوحة تحكم المدير</a>
                    <span>/</span>
                    <span>إدارة المستخدمين</span>
                </div>
            </div>
            <div class="header-left">
                <button class="notification-btn" onclick="toggleNotifications()">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">2</span>
                </button>
                <div class="user-menu">
                    <div class="user-info" onclick="toggleUserMenu()">
                        <img src="https://via.placeholder.com/40" alt="المدير" class="user-avatar">
                        <div class="user-details">
                            <span class="user-name">محمد جاسم</span>
                            <span class="user-role">مدير النظام</span>
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="user-dropdown" id="user-dropdown">
                        <a href="#" onclick="viewProfile()"><i class="fas fa-user"></i> الملف الشخصي</a>
                        <a href="#" onclick="openSettings()"><i class="fas fa-cog"></i> الإعدادات</a>
                        <a href="#" onclick="authSystem.logout()" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <a href="admin-dashboard.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
                <a href="user-management.html" class="nav-link active">
                    <i class="fas fa-users-cog"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a href="employees.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>الموظفين</span>
                </a>
                <a href="attendance.html" class="nav-link">
                    <i class="fas fa-calendar-check"></i>
                    <span>الحضور</span>
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </a>
                <a href="settings.html" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="page-header">
                <h1>إدارة المستخدمين</h1>
                <p>إضافة وتعديل وإدارة صلاحيات المستخدمين في النظام</p>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="openAddUserModal()">
                    <i class="fas fa-plus"></i>
                    إضافة مستخدم جديد
                </button>
                <button class="btn btn-secondary" onclick="exportUsers()">
                    <i class="fas fa-download"></i>
                    تصدير قائمة المستخدمين
                </button>
            </div>

            <!-- Users Table -->
            <div class="card">
                <div class="card-header">
                    <h3>قائمة المستخدمين</h3>
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="البحث في المستخدمين..." id="search-users">
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>نوع المستخدم</th>
                                    <th>القسم</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="users-table-body">
                                <tr>
                                    <td>
                                        <div class="user-info-cell">
                                            <img src="https://via.placeholder.com/40" alt="محمد جاسم" class="user-avatar-small">
                                            <span>محمد جاسم</span>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td><span class="badge badge-admin">مدير</span></td>
                                    <td>الإدارة العامة</td>
                                    <td><span class="status-badge active">نشط</span></td>
                                    <td>2024-01-01</td>
                                    <td>
                                        <div class="action-buttons-cell">
                                            <button class="btn-icon" onclick="editUser(1)" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon" onclick="viewUserDetails(1)" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="user-info-cell">
                                            <img src="https://via.placeholder.com/40" alt="سارة أحمد" class="user-avatar-small">
                                            <span>سارة أحمد</span>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td><span class="badge badge-supervisor">مشرف</span></td>
                                    <td>الموارد البشرية</td>
                                    <td><span class="status-badge active">نشط</span></td>
                                    <td>2024-01-15</td>
                                    <td>
                                        <div class="action-buttons-cell">
                                            <button class="btn-icon" onclick="editUser(2)" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon" onclick="viewUserDetails(2)" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon danger" onclick="deleteUser(2)" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="user-info-cell">
                                            <img src="https://via.placeholder.com/40" alt="علي محمد" class="user-avatar-small">
                                            <span>علي محمد</span>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td><span class="badge badge-employee">موظف</span></td>
                                    <td>المبيعات</td>
                                    <td><span class="status-badge active">نشط</span></td>
                                    <td>2024-02-01</td>
                                    <td>
                                        <div class="action-buttons-cell">
                                            <button class="btn-icon" onclick="editUser(3)" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon" onclick="viewUserDetails(3)" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon danger" onclick="deleteUser(3)" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Add User Modal -->
    <div id="add-user-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة مستخدم جديد</h3>
                <span class="close-modal" onclick="closeAddUserModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="add-user-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label>الاسم الكامل *</label>
                            <input type="text" name="fullName" required>
                        </div>
                        <div class="form-group">
                            <label>البريد الإلكتروني *</label>
                            <input type="email" name="email" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>كلمة المرور *</label>
                            <input type="password" name="password" required>
                        </div>
                        <div class="form-group">
                            <label>نوع المستخدم *</label>
                            <select name="userType" required>
                                <option value="">اختر نوع المستخدم</option>
                                <option value="admin">مدير</option>
                                <option value="supervisor">مشرف</option>
                                <option value="employee">موظف</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>القسم</label>
                            <input type="text" name="department">
                        </div>
                        <div class="form-group">
                            <label>المنصب</label>
                            <input type="text" name="position">
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeAddUserModal()">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة المستخدم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="auth-system.js"></script>
    <script src="user-management-script.js"></script>
</body>
</html>
