// Settings Script for KHALAiFAT Live Location
// Accessible by all authenticated users

document.addEventListener('DOMContentLoaded', function() {
    initializeSettings();
});

function initializeSettings() {
    // Load current user info
    loadCurrentUserInfo();
    
    // Setup sidebar based on user permissions
    setupSidebar();
    
    // Load saved settings
    loadSettings();
    
    // Setup form handlers
    setupFormHandlers();
}

function loadCurrentUserInfo() {
    const currentUser = authSystem.getCurrentUser();
    if (currentUser) {
        const userNameElement = document.getElementById('current-user-name');
        const userRoleElement = document.getElementById('current-user-role');
        
        if (userNameElement) {
            userNameElement.textContent = currentUser.name;
        }
        
        if (userRoleElement) {
            const roleText = getUserRoleText(currentUser.type);
            userRoleElement.textContent = roleText;
        }
    }
}

function setupSidebar() {
    const currentUser = authSystem.getCurrentUser();
    if (!currentUser) return;
    
    // Hide user management link for non-admin users
    const userManagementLink = document.getElementById('user-management-link');
    if (userManagementLink && !authSystem.canManageUsers()) {
        userManagementLink.style.display = 'none';
    }
    
    // Update dashboard link based on user type
    const dashboardLink = document.querySelector('a[href="admin-dashboard.html"]');
    if (dashboardLink) {
        dashboardLink.href = authSystem.getDashboardUrl();
    }
}

function getUserRoleText(type) {
    const roles = {
        'admin': 'مدير النظام',
        'supervisor': 'مشرف',
        'employee': 'موظف'
    };
    return roles[type] || 'مستخدم';
}

function loadSettings() {
    // Load settings from localStorage or use defaults
    const settings = getStoredSettings();
    
    // Apply settings to form elements
    applySettingsToForm(settings);
}

function getStoredSettings() {
    const defaultSettings = {
        systemName: 'KHALAiFAT Live Location',
        timezone: 'Asia/Riyadh',
        language: 'ar',
        workHours: 8,
        startTime: '08:00',
        endTime: '17:00',
        locationTracking: true,
        sessionTimeout: 60,
        activityLogging: true,
        twoFactorAuth: false,
        lateNotifications: true,
        absenceNotifications: true,
        weeklyReports: true
    };
    
    const stored = localStorage.getItem('systemSettings');
    return stored ? { ...defaultSettings, ...JSON.parse(stored) } : defaultSettings;
}

function applySettingsToForm(settings) {
    // Apply each setting to its corresponding form element
    const elements = {
        'input[value="KHALAiFAT Live Location"]': settings.systemName,
        'select option[value="' + settings.timezone + '"]': true,
        'select option[value="' + settings.language + '"]': true,
        'input[type="number"][value="8"]': settings.workHours,
        'input[type="time"][value="08:00"]': settings.startTime,
        'input[type="time"][value="17:00"]': settings.endTime,
        'input[type="number"][value="60"]': settings.sessionTimeout
    };
    
    // Apply text and number inputs
    Object.keys(elements).forEach(selector => {
        const element = document.querySelector(selector);
        if (element && typeof elements[selector] !== 'boolean') {
            element.value = elements[selector];
        } else if (element && elements[selector] === true) {
            element.selected = true;
        }
    });
    
    // Apply checkboxes
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    const checkboxSettings = [
        settings.locationTracking,
        settings.activityLogging,
        settings.twoFactorAuth,
        settings.lateNotifications,
        settings.absenceNotifications,
        settings.weeklyReports
    ];
    
    checkboxes.forEach((checkbox, index) => {
        if (index < checkboxSettings.length) {
            checkbox.checked = checkboxSettings[index];
        }
    });
}

function setupFormHandlers() {
    // Add change listeners to form elements
    const inputs = document.querySelectorAll('input, select');
    inputs.forEach(input => {
        input.addEventListener('change', markAsModified);
    });
}

function markAsModified() {
    // Add visual indication that settings have been modified
    const saveButton = document.querySelector('.btn-primary');
    if (saveButton && !saveButton.classList.contains('modified')) {
        saveButton.classList.add('modified');
        saveButton.innerHTML = '<i class="fas fa-save"></i> حفظ التغييرات *';
    }
}

function saveSettings() {
    try {
        // Collect all settings from form
        const settings = collectSettingsFromForm();
        
        // Validate settings
        if (!validateSettings(settings)) {
            return;
        }
        
        // Save to localStorage
        localStorage.setItem('systemSettings', JSON.stringify(settings));
        
        // Show success message
        showNotification('تم حفظ الإعدادات بنجاح', 'success');
        
        // Remove modified indicator
        const saveButton = document.querySelector('.btn-primary');
        if (saveButton) {
            saveButton.classList.remove('modified');
            saveButton.innerHTML = '<i class="fas fa-save"></i> حفظ الإعدادات';
        }
        
    } catch (error) {
        console.error('Error saving settings:', error);
        showNotification('حدث خطأ أثناء حفظ الإعدادات', 'error');
    }
}

function collectSettingsFromForm() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    
    return {
        systemName: document.querySelector('input[readonly]').value,
        timezone: document.querySelector('select').value,
        language: document.querySelectorAll('select')[1].value,
        workHours: parseInt(document.querySelector('input[type="number"]').value),
        startTime: document.querySelector('input[type="time"]').value,
        endTime: document.querySelectorAll('input[type="time"]')[1].value,
        locationTracking: checkboxes[0].checked,
        sessionTimeout: parseInt(document.querySelectorAll('input[type="number"]')[1].value),
        activityLogging: checkboxes[1].checked,
        twoFactorAuth: checkboxes[2].checked,
        lateNotifications: checkboxes[3].checked,
        absenceNotifications: checkboxes[4].checked,
        weeklyReports: checkboxes[5].checked
    };
}

function validateSettings(settings) {
    // Validate work hours
    if (settings.workHours < 1 || settings.workHours > 24) {
        showNotification('ساعات العمل يجب أن تكون بين 1 و 24 ساعة', 'error');
        return false;
    }
    
    // Validate session timeout
    if (settings.sessionTimeout < 15 || settings.sessionTimeout > 480) {
        showNotification('مدة انتهاء الجلسة يجب أن تكون بين 15 و 480 دقيقة', 'error');
        return false;
    }
    
    // Validate time format
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(settings.startTime) || !timeRegex.test(settings.endTime)) {
        showNotification('تنسيق الوقت غير صحيح', 'error');
        return false;
    }
    
    return true;
}

function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
        // Clear stored settings
        localStorage.removeItem('systemSettings');
        
        // Reload page to apply default settings
        window.location.reload();
    }
}

// Header functions
function toggleUserMenu() {
    const dropdown = document.getElementById('user-dropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

function toggleNotifications() {
    showNotification('سيتم تطوير نظام الإشعارات قريباً', 'info');
}

function viewProfile() {
    showNotification('سيتم فتح صفحة الملف الشخصي قريباً', 'info');
}

function openSettings() {
    // Already on settings page
    showNotification('أنت في صفحة الإعدادات حالياً', 'info');
}

// Notification system
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        border-radius: 8px;
        padding: 1rem;
        max-width: 400px;
        z-index: 10000;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        animation: slideInRight 0.3s ease;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Close dropdown when clicking outside
document.addEventListener('click', function(e) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('user-dropdown');
    
    if (dropdown && !userMenu.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});

// Export functions for global access
window.saveSettings = saveSettings;
window.resetSettings = resetSettings;
window.toggleUserMenu = toggleUserMenu;
window.toggleNotifications = toggleNotifications;
window.viewProfile = viewProfile;
window.openSettings = openSettings;
