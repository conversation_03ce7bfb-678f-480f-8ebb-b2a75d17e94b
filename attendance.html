<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحضور والانصراف | موقوت</title>
    <link rel="icon" type="image/x-icon" href="https://cdn.mawqoot.com/static/images/favicon.ico">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="dashboard-style.css">
    <link rel="stylesheet" href="attendance-style.css">
</head>

<body>
    <!-- Authentication Check Script -->
    <script src="auth-system.js"></script>
    <script>
        // Check authentication and permissions
        if (!authSystem.isLoggedIn()) {
            window.location.href = 'login.html';
        } else {
            const currentUser = authSystem.getCurrentUser();
            // Only admin and supervisor can access attendance management page
            if (!authSystem.hasPermission('view_attendance')) {
                alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
                window.location.href = authSystem.getDashboardUrl();
            }
        }
    </script>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <img src="https://cdn.mawqoot.com/static/images/logo/logo-ar.png" alt="موقوت" class="sidebar-logo">
            <button class="sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul>
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="employees.html" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>الموظفين</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="attendance.html" class="nav-link">
                        <i class="fas fa-clock"></i>
                        <span>الحضور والانصراف</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="leaves.html" class="nav-link">
                        <i class="fas fa-calendar-times"></i>
                        <span>الإجازات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="violations.html" class="nav-link">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>المخالفات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="reports.html" class="nav-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>التقارير</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="payroll.html" class="nav-link">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>الرواتب</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="settings.html" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name">أحمد محمد</span>
                    <span class="user-role">مدير النظام</span>
                </div>
            </div>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        <!-- Top Header -->
        <header class="top-header">
            <div class="header-left">
                <button class="mobile-menu-btn" id="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>الحضور والانصراف</h1>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <div class="current-time">
                        <div class="time-display" id="current-time-display">
                            <span id="current-time">00:00:00</span>
                            <span id="current-date">الأحد، 1 يناير 2024</span>
                        </div>
                    </div>
                    <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Attendance Content -->
        <div class="attendance-content">
            <!-- Quick Actions -->
            <div class="quick-actions-section">
                <div class="action-card check-in">
                    <div class="action-icon">
                        <i class="fas fa-sign-in-alt"></i>
                    </div>
                    <div class="action-content">
                        <h3>تسجيل الحضور</h3>
                        <p>سجل حضورك لبدء يوم العمل</p>
                        <button class="action-btn" onclick="checkIn()">
                            <i class="fas fa-clock"></i>
                            <span>تسجيل الحضور</span>
                        </button>
                    </div>
                </div>

                <div class="action-card check-out">
                    <div class="action-icon">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <div class="action-content">
                        <h3>تسجيل الانصراف</h3>
                        <p>سجل انصرافك عند انتهاء يوم العمل</p>
                        <button class="action-btn" onclick="checkOut()">
                            <i class="fas fa-clock"></i>
                            <span>تسجيل الانصراف</span>
                        </button>
                    </div>
                </div>

                <div class="action-card break">
                    <div class="action-icon">
                        <i class="fas fa-coffee"></i>
                    </div>
                    <div class="action-content">
                        <h3>استراحة</h3>
                        <p>سجل بداية أو نهاية فترة الاستراحة</p>
                        <button class="action-btn" onclick="toggleBreak()">
                            <i class="fas fa-pause"></i>
                            <span>بدء الاستراحة</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Today's Status -->
            <div class="today-status">
                <div class="status-header">
                    <h3>حالة اليوم</h3>
                    <div class="status-date" id="status-date">الأحد، 1 يناير 2024</div>
                </div>

                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-icon check-in">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <div class="status-details">
                            <span class="status-label">وقت الحضور</span>
                            <span class="status-value" id="checkin-time">--:--</span>
                        </div>
                    </div>

                    <div class="status-item">
                        <div class="status-icon check-out">
                            <i class="fas fa-sign-out-alt"></i>
                        </div>
                        <div class="status-details">
                            <span class="status-label">وقت الانصراف</span>
                            <span class="status-value" id="checkout-time">--:--</span>
                        </div>
                    </div>

                    <div class="status-item">
                        <div class="status-icon working">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="status-details">
                            <span class="status-label">ساعات العمل</span>
                            <span class="status-value" id="working-hours">0:00</span>
                        </div>
                    </div>

                    <div class="status-item">
                        <div class="status-icon break">
                            <i class="fas fa-coffee"></i>
                        </div>
                        <div class="status-details">
                            <span class="status-label">وقت الاستراحة</span>
                            <span class="status-value" id="break-time">0:00</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="filters-section">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="attendance-search" placeholder="البحث عن موظف...">
                </div>

                <div class="filters">
                    <input type="date" id="date-filter" class="date-input">

                    <select id="department-filter">
                        <option value="">جميع الأقسام</option>
                        <option value="it">تقنية المعلومات</option>
                        <option value="hr">الموارد البشرية</option>
                        <option value="finance">المالية</option>
                        <option value="marketing">التسويق</option>
                        <option value="sales">المبيعات</option>
                    </select>

                    <select id="status-filter">
                        <option value="">جميع الحالات</option>
                        <option value="present">حاضر</option>
                        <option value="absent">غائب</option>
                        <option value="late">متأخر</option>
                        <option value="on-break">في استراحة</option>
                    </select>

                    <button class="filter-btn" onclick="exportAttendance()">
                        <i class="fas fa-download"></i>
                        <span>تصدير</span>
                    </button>
                </div>
            </div>

            <!-- Attendance Stats -->
            <div class="attendance-stats">
                <div class="stat-item present">
                    <div class="stat-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-details">
                        <span class="stat-number">142</span>
                        <span class="stat-label">حاضر اليوم</span>
                    </div>
                </div>

                <div class="stat-item absent">
                    <div class="stat-icon">
                        <i class="fas fa-user-times"></i>
                    </div>
                    <div class="stat-details">
                        <span class="stat-number">14</span>
                        <span class="stat-label">غائب اليوم</span>
                    </div>
                </div>

                <div class="stat-item late">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-details">
                        <span class="stat-number">8</span>
                        <span class="stat-label">متأخر اليوم</span>
                    </div>
                </div>

                <div class="stat-item on-break">
                    <div class="stat-icon">
                        <i class="fas fa-coffee"></i>
                    </div>
                    <div class="stat-details">
                        <span class="stat-number">12</span>
                        <span class="stat-label">في استراحة</span>
                    </div>
                </div>
            </div>

            <!-- Attendance Table -->
            <div class="attendance-table-container">
                <table class="attendance-table" id="attendance-table">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>القسم</th>
                            <th>وقت الحضور</th>
                            <th>وقت الانصراف</th>
                            <th>ساعات العمل</th>
                            <th>الحالة</th>
                            <th>ملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="attendance-tbody">
                        <!-- Attendance rows will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="pagination">
                <button class="pagination-btn" id="prev-page" disabled>
                    <i class="fas fa-chevron-right"></i>
                </button>
                <div class="pagination-info">
                    <span>صفحة <span id="current-page">1</span> من <span id="total-pages">10</span></span>
                </div>
                <button class="pagination-btn" id="next-page">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Manual Attendance Modal -->
    <div id="manual-attendance-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تسجيل حضور يدوي</h3>
                <span class="close-modal" onclick="closeModal('manual-attendance')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="manual-attendance-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label>الموظف *</label>
                            <select name="employeeId" required>
                                <option value="">اختر الموظف</option>
                                <option value="1">أحمد محمد</option>
                                <option value="2">فاطمة علي</option>
                                <option value="3">محمد خالد</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>التاريخ *</label>
                            <input type="date" name="date" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>وقت الحضور</label>
                            <input type="time" name="checkInTime">
                        </div>
                        <div class="form-group">
                            <label>وقت الانصراف</label>
                            <input type="time" name="checkOutTime">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>ملاحظات</label>
                        <textarea name="notes" rows="3" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn-secondary"
                            onclick="closeModal('manual-attendance')">إلغاء</button>
                        <button type="submit" class="btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Custom JavaScript -->
    <script src="dashboard-script.js"></script>
    <script src="attendance-script.js"></script>
</body>

</html>