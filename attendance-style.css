/* Attendance Page Specific Styles */

.attendance-content {
    padding: 2rem;
}

/* Header Time Display */
.current-time {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-left: 1rem;
}

.time-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

#current-time {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    font-family: 'Cairo', sans-serif;
}

#current-date {
    font-size: 0.875rem;
    color: #666;
}

/* Quick Actions Section */
.quick-actions-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.action-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.action-card .action-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.action-card.check-in .action-icon {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.action-card.check-out .action-icon {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
}

.action-card.break .action-icon {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.action-content {
    flex: 1;
}

.action-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.action-content p {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 10px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

/* Today's Status */
.today-status {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.status-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
}

.status-date {
    color: #666;
    font-size: 0.875rem;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.status-item .status-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.status-item .status-icon.check-in {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.status-item .status-icon.check-out {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
}

.status-item .status-icon.working {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.status-item .status-icon.break {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.status-details {
    display: flex;
    flex-direction: column;
}

.status-label {
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.status-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: #333;
}

/* Filters Section */
.filters-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-box input {
    width: 100%;
    padding: 12px 40px 12px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
}

.filters {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.filters select,
.date-input {
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    background: white;
    min-width: 150px;
    transition: border-color 0.3s ease;
}

.filters select:focus,
.date-input:focus {
    outline: none;
    border-color: #667eea;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 16px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background: #218838;
    transform: translateY(-2px);
}

/* Attendance Stats */
.attendance-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-item {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.stat-item .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.stat-item.present .stat-icon {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stat-item.absent .stat-icon {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
}

.stat-item.late .stat-icon {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.stat-item.on-break .stat-icon {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
}

.stat-details {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #666;
}

/* Attendance Table */
.attendance-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.attendance-table {
    width: 100%;
    border-collapse: collapse;
}

.attendance-table th,
.attendance-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.attendance-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
    z-index: 10;
}

.attendance-table tbody tr {
    transition: background-color 0.3s ease;
}

.attendance-table tbody tr:hover {
    background: #f8f9fa;
}

.employee-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.employee-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.employee-details {
    display: flex;
    flex-direction: column;
}

.employee-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.employee-department {
    font-size: 0.875rem;
    color: #666;
}

.time-display-cell {
    font-weight: 600;
    color: #333;
}

.time-display-cell.late {
    color: #dc3545;
}

.time-display-cell.early {
    color: #28a745;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
}

.status-badge.present {
    background: #d4edda;
    color: #155724;
}

.status-badge.absent {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.late {
    background: #fff3cd;
    color: #856404;
}

.status-badge.on-break {
    background: #e2e3f1;
    color: #383d41;
}

.working-hours {
    font-weight: 600;
    color: #667eea;
}

.notes-cell {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #666;
    font-size: 0.875rem;
}

.actions-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn-small {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.action-btn-small.edit {
    background: #667eea;
    color: white;
}

.action-btn-small.edit:hover {
    background: #5a6fd8;
}

.action-btn-small.delete {
    background: #dc3545;
    color: white;
}

.action-btn-small.delete:hover {
    background: #c82333;
}

.action-btn-small.manual {
    background: #28a745;
    color: white;
}

.action-btn-small.manual:hover {
    background: #218838;
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.pagination-btn {
    width: 40px;
    height: 40px;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
    border-color: #667eea;
    color: #667eea;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    color: #666;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .quick-actions-section {
        grid-template-columns: repeat(2, 1fr);
    }

    .status-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .attendance-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .attendance-table-container {
        overflow-x: auto;
    }

    .attendance-table {
        min-width: 900px;
    }
}

@media (max-width: 768px) {
    .attendance-content {
        padding: 1rem;
    }

    .quick-actions-section {
        grid-template-columns: 1fr;
    }

    .action-card {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
    }

    .action-card .action-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .filters {
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .status-grid {
        grid-template-columns: 1fr;
    }

    .attendance-stats {
        grid-template-columns: 1fr;
    }

    .attendance-table th,
    .attendance-table td {
        padding: 0.75rem 0.5rem;
    }

    .employee-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .actions-buttons {
        flex-direction: column;
    }

    .current-time {
        margin-left: 0;
        margin-top: 1rem;
    }
}

@media (max-width: 480px) {
    .action-card {
        padding: 1rem;
    }

    .action-card .action-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .action-content h3 {
        font-size: 1rem;
    }

    .today-status {
        padding: 1.5rem;
    }

    .status-item {
        padding: 0.75rem;
    }

    .status-item .status-icon {
        width: 35px;
        height: 35px;
        font-size: 0.875rem;
    }

    .stat-item {
        padding: 1rem;
    }

    .stat-item .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .stat-number {
        font-size: 1.25rem;
    }

    .attendance-table {
        font-size: 0.875rem;
    }

    .pagination {
        padding: 0.75rem;
    }

    .pagination-btn {
        width: 35px;
        height: 35px;
    }
}

/* GPS Location Indicator */
.location-indicator {
    margin-left: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.location-indicator.valid {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.location-indicator.invalid {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #333;
}

.location-indicator.error {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
    color: white;
}

.location-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.location-status i {
    font-size: 1rem;
}

.location-status small {
    font-size: 0.75rem;
    opacity: 0.9;
    margin-right: 0.5rem;
}

.location-indicator.valid .location-status small {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
}

.location-indicator.invalid .location-status small {
    background: rgba(0, 0, 0, 0.1);
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
}

/* Location Permission Request */
.location-permission-banner {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.location-permission-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.location-permission-content i {
    font-size: 1.5rem;
    color: #ffd700;
}

.location-permission-text h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.location-permission-text p {
    margin: 0;
    font-size: 0.875rem;
    opacity: 0.9;
}

.location-permission-actions {
    display: flex;
    gap: 0.75rem;
}

.location-permission-btn {
    padding: 0.5rem 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.location-permission-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.location-permission-btn.primary {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.location-permission-btn.primary:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.7);
}

/* GPS Status in Action Cards */
.action-card.gps-required {
    position: relative;
    opacity: 0.7;
    pointer-events: none;
}

.action-card.gps-required::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-card.gps-required::before {
    content: '🔒 يتطلب تحديد الموقع';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
}

/* Location accuracy indicator */
.location-accuracy {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 0.25rem;
}

.location-accuracy.high {
    color: #28a745;
}

.location-accuracy.medium {
    color: #ffc107;
}

.location-accuracy.low {
    color: #dc3545;
}

/* Responsive adjustments for location indicator */
@media (max-width: 768px) {
    .location-indicator {
        margin-left: 0;
        margin-top: 0.5rem;
        width: 100%;
        text-align: center;
    }

    .location-permission-banner {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .location-permission-actions {
        justify-content: center;
    }

    .action-card.gps-required::before {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
}