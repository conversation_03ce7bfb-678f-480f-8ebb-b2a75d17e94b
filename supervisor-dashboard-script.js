// Supervisor Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication and permissions
    if (!authSystem.isLoggedIn()) {
        window.location.href = 'login.html';
        return;
    }

    const currentUser = authSystem.getCurrentUser();
    if (currentUser.type !== authSystem.userTypes.SUPERVISOR) {
        window.location.href = authSystem.getDashboardUrl();
        return;
    }

    // Initialize dashboard
    initializeDashboard();
    loadDashboardData();
    initializeChart();
    setupPermissions();
    
    // Update user info in header
    updateUserInfo();
});

// Initialize dashboard components
function initializeDashboard() {
    // Set up event listeners
    setupEventListeners();
    
    // Load real-time data
    setInterval(updateRealTimeData, 30000); // Update every 30 seconds
    
    // Initialize interactive elements
    initializeInteractiveElements();
}

// Setup event listeners
function setupEventListeners() {
    // User menu toggle
    document.addEventListener('click', function(e) {
        const userMenu = document.getElementById('user-dropdown');
        const userInfo = document.querySelector('.user-info');
        
        if (!userInfo.contains(e.target)) {
            userMenu.classList.remove('show');
        }
    });
    
    // Notification panel toggle
    document.addEventListener('click', function(e) {
        const notificationsPanel = document.getElementById('notifications-panel');
        const notificationBtn = document.querySelector('.notification-btn');
        
        if (!notificationBtn.contains(e.target) && !notificationsPanel.contains(e.target)) {
            notificationsPanel.classList.remove('show');
        }
    });
}

// Setup permissions based on user
function setupPermissions() {
    const currentUser = authSystem.getCurrentUser();
    const permissionList = document.getElementById('permission-list');
    
    // Clear existing permissions
    permissionList.innerHTML = '';
    
    // Check and display permissions
    const permissions = [
        { key: 'view_employees', label: 'عرض الموظفين', icon: 'fa-users' },
        { key: 'view_attendance', label: 'عرض الحضور', icon: 'fa-calendar-check' },
        { key: 'view_reports', label: 'عرض التقارير', icon: 'fa-chart-bar' },
        { key: 'manage_team', label: 'إدارة الفريق', icon: 'fa-users-cog' },
        { key: 'approve_leaves', label: 'الموافقة على الإجازات', icon: 'fa-calendar-times' }
    ];
    
    permissions.forEach(permission => {
        const hasPermission = authSystem.hasPermission(permission.key);
        const permissionItem = document.createElement('div');
        permissionItem.className = `permission-item ${hasPermission ? 'granted' : 'denied'}`;
        permissionItem.innerHTML = `
            <i class="fas ${hasPermission ? 'fa-check' : 'fa-times'}"></i>
            <i class="fas ${permission.icon}"></i>
            <span>${permission.label}</span>
        `;
        permissionList.appendChild(permissionItem);
        
        // Disable navigation links based on permissions
        if (!hasPermission) {
            const navLink = document.getElementById(`${permission.key.replace('view_', '').replace('manage_', '')}-link`);
            if (navLink) {
                navLink.classList.add('disabled');
                navLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    showNotification('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'warning');
                });
            }
        }
    });
    
    // Disable action buttons based on permissions
    disableActionsBasedOnPermissions();
}

// Disable actions based on permissions
function disableActionsBasedOnPermissions() {
    const currentUser = authSystem.getCurrentUser();
    
    // Check specific permissions for actions
    const actionPermissions = [
        { elementId: 'team-attendance-btn', permission: 'view_attendance' },
        { elementId: 'team-report-btn', permission: 'view_reports' },
        { elementId: 'mark-attendance-action', permission: 'manage_team' },
        { elementId: 'team-reports-action', permission: 'view_reports' },
        { elementId: 'manage-leaves-action', permission: 'approve_leaves' },
        { elementId: 'send-notification-action', permission: 'manage_team' }
    ];
    
    actionPermissions.forEach(({ elementId, permission }) => {
        const element = document.getElementById(elementId);
        if (element && !authSystem.hasPermission(permission)) {
            element.classList.add('disabled');
            element.disabled = true;
            element.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                showNotification('ليس لديك صلاحية لتنفيذ هذا الإجراء', 'warning');
            });
        }
    });
}

// Toggle user menu
function toggleUserMenu() {
    const dropdown = document.getElementById('user-dropdown');
    dropdown.classList.toggle('show');
}

// Toggle notifications panel
function toggleNotifications() {
    const panel = document.getElementById('notifications-panel');
    panel.classList.toggle('show');
}

// Update user info in header
function updateUserInfo() {
    const currentUser = authSystem.getCurrentUser();
    const userNameElement = document.querySelector('.user-name');
    const userRoleElement = document.querySelector('.user-role');
    const welcomeTitle = document.querySelector('.welcome-content h1');
    
    if (userNameElement) userNameElement.textContent = currentUser.name;
    if (userRoleElement) userRoleElement.textContent = 'مشرف';
    if (welcomeTitle) welcomeTitle.textContent = `مرحباً، ${currentUser.name}`;
}

// Load dashboard data
function loadDashboardData() {
    // Simulate loading team data from API
    const teamData = {
        teamSize: 12,
        teamPresent: 9,
        teamLate: 2,
        teamAbsent: 1,
        teamMembers: [
            {
                name: 'أحمد محمد',
                position: 'مطور واجهات',
                status: 'present',
                time: '8:30 ص',
                avatar: 'https://via.placeholder.com/40'
            },
            {
                name: 'فاطمة علي',
                position: 'مصممة جرافيك',
                status: 'late',
                time: '9:15 ص',
                avatar: 'https://via.placeholder.com/40'
            },
            {
                name: 'محمد أحمد',
                position: 'مطور خلفية',
                status: 'absent',
                time: 'إجازة مرضية',
                avatar: 'https://via.placeholder.com/40'
            },
            {
                name: 'نور الدين',
                position: 'محلل أنظمة',
                status: 'present',
                time: '8:45 ص',
                avatar: 'https://via.placeholder.com/40'
            }
        ]
    };
    
    // Update stats
    updateTeamStats(teamData);
    
    // Update team overview
    updateTeamOverview(teamData.teamMembers);
}

// Update team statistics
function updateTeamStats(data) {
    document.getElementById('team-size').textContent = data.teamSize;
    document.getElementById('team-present').textContent = data.teamPresent;
    document.getElementById('team-late').textContent = data.teamLate;
    document.getElementById('team-absent').textContent = data.teamAbsent;
    
    // Update attendance percentage
    const attendanceRate = Math.round((data.teamPresent / data.teamSize) * 100);
    const attendanceElement = document.querySelector('.stat-card:nth-child(2) .stat-change');
    if (attendanceElement) {
        attendanceElement.textContent = `${attendanceRate}% من الفريق`;
    }
}

// Update team overview
function updateTeamOverview(members) {
    const teamOverview = document.querySelector('.team-overview');
    if (!teamOverview) return;
    
    teamOverview.innerHTML = '';
    
    members.forEach(member => {
        const memberElement = document.createElement('div');
        memberElement.className = 'team-member';
        
        const statusClass = member.status === 'present' ? 'online' : 
                           member.status === 'late' ? 'warning' : 'offline';
        
        const statusText = member.status === 'present' ? `حاضر - ${member.time}` :
                          member.status === 'late' ? `متأخر - ${member.time}` :
                          `غائب - ${member.time}`;
        
        const statusTextClass = member.status;
        
        memberElement.innerHTML = `
            <div class="member-avatar">
                <img src="${member.avatar}" alt="${member.name}">
                <div class="status-indicator ${statusClass}"></div>
            </div>
            <div class="member-info">
                <h4>${member.name}</h4>
                <p>${member.position}</p>
                <span class="status-text ${statusTextClass}">${statusText}</span>
            </div>
        `;
        
        teamOverview.appendChild(memberElement);
    });
}

// Initialize team performance chart
function initializeChart() {
    const ctx = document.getElementById('teamPerformanceChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['حاضر', 'متأخر', 'غائب'],
            datasets: [{
                data: [9, 2, 1],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#dc3545'
                ],
                borderWidth: 0,
                cutout: '60%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

// Update real-time data
function updateRealTimeData() {
    // Simulate real-time updates for team data
    const presentElement = document.getElementById('team-present');
    const lateElement = document.getElementById('team-late');
    
    if (presentElement) {
        const currentPresent = parseInt(presentElement.textContent);
        // Randomly update present count (simulate real-time changes)
        const newPresent = Math.max(8, Math.min(12, currentPresent + Math.floor(Math.random() * 2) - 1));
        presentElement.textContent = newPresent;
    }
    
    if (lateElement) {
        const currentLate = parseInt(lateElement.textContent);
        // Randomly update late count
        const newLate = Math.max(0, Math.min(4, currentLate + Math.floor(Math.random() * 2) - 1));
        lateElement.textContent = newLate;
    }
}

// Initialize interactive elements
function initializeInteractiveElements() {
    // Add hover effects
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('disabled')) {
                this.style.transform = 'translateY(-5px)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// Action functions
function viewTeamAttendance() {
    if (authSystem.hasPermission('view_attendance')) {
        window.location.href = 'attendance.html?view=team';
    } else {
        showNotification('ليس لديك صلاحية لعرض بيانات الحضور', 'warning');
    }
}

function generateTeamReport() {
    if (authSystem.hasPermission('view_reports')) {
        window.location.href = 'reports.html?type=team';
    } else {
        showNotification('ليس لديك صلاحية لإنشاء التقارير', 'warning');
    }
}

function markAttendance() {
    if (authSystem.hasPermission('manage_team')) {
        window.location.href = 'attendance.html?action=mark';
    } else {
        showNotification('ليس لديك صلاحية لتسجيل الحضور', 'warning');
    }
}

function viewTeamReports() {
    if (authSystem.hasPermission('view_reports')) {
        window.location.href = 'reports.html?view=team';
    } else {
        showNotification('ليس لديك صلاحية لعرض التقارير', 'warning');
    }
}

function manageLeaves() {
    if (authSystem.hasPermission('approve_leaves')) {
        showNotification('سيتم فتح صفحة إدارة الإجازات قريباً', 'info');
    } else {
        showNotification('ليس لديك صلاحية لإدارة الإجازات', 'warning');
    }
}

function sendNotification() {
    if (authSystem.hasPermission('manage_team')) {
        showNotification('سيتم فتح نافذة إرسال الإشعارات قريباً', 'info');
    } else {
        showNotification('ليس لديك صلاحية لإرسال الإشعارات', 'warning');
    }
}

// Profile function
function viewProfile() {
    showNotification('سيتم فتح صفحة الملف الشخصي قريباً', 'info');
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 90px;
        right: 20px;
        background: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        transform: translateX(400px);
        transition: all 0.3s ease;
        border-right: 4px solid ${getNotificationColor(type)};
    `;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Hide notification
    setTimeout(() => {
        notification.style.transform = 'translateX(400px)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 4000);
}

function getNotificationIcon(type) {
    const icons = {
        success: 'fa-check-circle',
        error: 'fa-exclamation-circle',
        warning: 'fa-exclamation-triangle',
        info: 'fa-info-circle'
    };
    return icons[type] || 'fa-info-circle';
}

function getNotificationColor(type) {
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };
    return colors[type] || '#17a2b8';
}

// Export functions for global access
window.toggleUserMenu = toggleUserMenu;
window.toggleNotifications = toggleNotifications;
window.viewTeamAttendance = viewTeamAttendance;
window.generateTeamReport = generateTeamReport;
window.markAttendance = markAttendance;
window.viewTeamReports = viewTeamReports;
window.manageLeaves = manageLeaves;
window.sendNotification = sendNotification;
window.viewProfile = viewProfile;
