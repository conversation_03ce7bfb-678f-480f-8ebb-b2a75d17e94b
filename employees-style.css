/* Employees Page Specific Styles */

.employees-content {
    padding: 2rem;
}

/* Filters Section */
.filters-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-box input {
    width: 100%;
    padding: 12px 40px 12px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
}

.filters {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.filters select {
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    background: white;
    min-width: 150px;
    transition: border-color 0.3s ease;
}

.filters select:focus {
    outline: none;
    border-color: #667eea;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 16px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background: #218838;
    transform: translateY(-2px);
}

/* Employees Stats */
.employees-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-item {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.stat-item .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-item .stat-icon.active {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stat-item .stat-icon.inactive {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
}

.stat-item .stat-icon.leave {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.stat-details {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #666;
}

/* Employees Table */
.employees-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.employees-table {
    width: 100%;
    border-collapse: collapse;
}

.employees-table th,
.employees-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.employees-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
    z-index: 10;
}

.employees-table tbody tr {
    transition: background-color 0.3s ease;
}

.employees-table tbody tr:hover {
    background: #f8f9fa;
}

.employee-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.employee-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.employee-details {
    display: flex;
    flex-direction: column;
}

.employee-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.employee-email {
    font-size: 0.875rem;
    color: #666;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.on-leave {
    background: #fff3cd;
    color: #856404;
}

.salary-amount {
    font-weight: 600;
    color: #28a745;
}

.actions-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn-small {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.action-btn-small.edit {
    background: #667eea;
    color: white;
}

.action-btn-small.edit:hover {
    background: #5a6fd8;
}

.action-btn-small.delete {
    background: #dc3545;
    color: white;
}

.action-btn-small.delete:hover {
    background: #c82333;
}

.action-btn-small.view {
    background: #28a745;
    color: white;
}

.action-btn-small.view:hover {
    background: #218838;
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.pagination-btn {
    width: 40px;
    height: 40px;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
    border-color: #667eea;
    color: #667eea;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    color: #666;
    font-size: 0.875rem;
}

/* Large Modal */
.large-modal {
    max-width: 800px;
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h4 {
    color: #333;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #667eea;
    display: inline-block;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-actions .btn-primary {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 10px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
}

.header-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .employees-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .employees-table-container {
        overflow-x: auto;
    }
    
    .employees-table {
        min-width: 800px;
    }
}

@media (max-width: 768px) {
    .employees-content {
        padding: 1rem;
    }
    
    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .filters {
        justify-content: space-between;
        flex-wrap: wrap;
    }
    
    .employees-stats {
        grid-template-columns: 1fr;
    }
    
    .employees-table th,
    .employees-table td {
        padding: 0.75rem 0.5rem;
    }
    
    .employee-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .actions-buttons {
        flex-direction: column;
    }
    
    .large-modal {
        max-width: 95%;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stat-item {
        padding: 1rem;
    }
    
    .stat-item .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .stat-number {
        font-size: 1.25rem;
    }
    
    .employees-table {
        font-size: 0.875rem;
    }
    
    .pagination {
        padding: 0.75rem;
    }
    
    .pagination-btn {
        width: 35px;
        height: 35px;
    }
}
