// Admin Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication and permissions
    if (!authSystem.isLoggedIn()) {
        window.location.href = 'login.html';
        return;
    }

    const currentUser = authSystem.getCurrentUser();
    if (currentUser.type !== authSystem.userTypes.ADMIN) {
        window.location.href = authSystem.getDashboardUrl();
        return;
    }

    // Initialize dashboard
    initializeDashboard();
    loadDashboardData();
    initializeChart();
    
    // Update user info in header
    updateUserInfo();
});

// Initialize dashboard components
function initializeDashboard() {
    // Set up event listeners
    setupEventListeners();
    
    // Load real-time data
    setInterval(updateRealTimeData, 30000); // Update every 30 seconds
    
    // Initialize tooltips and interactive elements
    initializeInteractiveElements();
}

// Setup event listeners
function setupEventListeners() {
    // User menu toggle
    document.addEventListener('click', function(e) {
        const userMenu = document.getElementById('user-dropdown');
        const userInfo = document.querySelector('.user-info');
        
        if (!userInfo.contains(e.target)) {
            userMenu.classList.remove('show');
        }
    });
    
    // Notification panel toggle
    document.addEventListener('click', function(e) {
        const notificationsPanel = document.getElementById('notifications-panel');
        const notificationBtn = document.querySelector('.notification-btn');
        
        if (!notificationBtn.contains(e.target) && !notificationsPanel.contains(e.target)) {
            notificationsPanel.classList.remove('show');
        }
    });
}

// Toggle user menu
function toggleUserMenu() {
    const dropdown = document.getElementById('user-dropdown');
    dropdown.classList.toggle('show');
}

// Toggle notifications panel
function toggleNotifications() {
    const panel = document.getElementById('notifications-panel');
    panel.classList.toggle('show');
}

// Update user info in header
function updateUserInfo() {
    const currentUser = authSystem.getCurrentUser();
    const userNameElement = document.querySelector('.user-name');
    const userRoleElement = document.querySelector('.user-role');
    const welcomeTitle = document.querySelector('.welcome-content h1');
    
    if (userNameElement) userNameElement.textContent = currentUser.name;
    if (userRoleElement) userRoleElement.textContent = 'مدير النظام';
    if (welcomeTitle) welcomeTitle.textContent = `مرحباً، ${currentUser.name}`;
}

// Load dashboard data
function loadDashboardData() {
    // Simulate loading data from API
    const dashboardData = {
        totalEmployees: 24,
        presentToday: 18,
        lateToday: 3,
        absentToday: 3,
        attendanceRate: 75,
        recentActivities: [
            {
                type: 'employee_added',
                message: 'تم إضافة موظف جديد: سارة أحمد',
                time: 'منذ 30 دقيقة',
                icon: 'fa-user-plus',
                iconClass: 'success'
            },
            {
                type: 'login',
                message: 'تسجيل دخول: محمد علي',
                time: 'منذ ساعة',
                icon: 'fa-sign-in-alt',
                iconClass: 'info'
            },
            {
                type: 'late_arrival',
                message: 'تأخير: فاطمة محمد - 15 دقيقة',
                time: 'منذ ساعتين',
                icon: 'fa-exclamation-triangle',
                iconClass: 'warning'
            },
            {
                type: 'report_generated',
                message: 'تم إنشاء تقرير: تقرير الحضور الشهري',
                time: 'منذ 3 ساعات',
                icon: 'fa-file-alt',
                iconClass: 'success'
            }
        ]
    };
    
    // Update stats
    updateStats(dashboardData);
    
    // Update recent activities
    updateRecentActivities(dashboardData.recentActivities);
}

// Update statistics
function updateStats(data) {
    document.getElementById('total-employees').textContent = data.totalEmployees;
    document.getElementById('present-today').textContent = data.presentToday;
    document.getElementById('late-today').textContent = data.lateToday;
    document.getElementById('absent-today').textContent = data.absentToday;
    
    // Update attendance rate
    const attendanceRateElement = document.querySelector('.stat-card:nth-child(2) .stat-change');
    if (attendanceRateElement) {
        attendanceRateElement.textContent = `${data.attendanceRate}% معدل الحضور`;
    }
}

// Update recent activities
function updateRecentActivities(activities) {
    const activityList = document.querySelector('.activity-list');
    if (!activityList) return;
    
    activityList.innerHTML = '';
    
    activities.forEach(activity => {
        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';
        activityItem.innerHTML = `
            <div class="activity-icon ${activity.iconClass}">
                <i class="fas ${activity.icon}"></i>
            </div>
            <div class="activity-content">
                <p>${activity.message}</p>
                <span class="activity-time">${activity.time}</span>
            </div>
        `;
        activityList.appendChild(activityItem);
    });
}

// Initialize attendance chart
function initializeChart() {
    const ctx = document.getElementById('attendanceChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
            datasets: [{
                label: 'معدل الحضور',
                data: [85, 78, 92, 88, 76, 89, 0],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            elements: {
                point: {
                    hoverRadius: 8
                }
            }
        }
    });
}

// Update real-time data
function updateRealTimeData() {
    // Simulate real-time updates
    const presentElement = document.getElementById('present-today');
    const lateElement = document.getElementById('late-today');
    
    if (presentElement) {
        const currentPresent = parseInt(presentElement.textContent);
        // Randomly update present count (simulate real-time changes)
        const newPresent = Math.max(15, Math.min(25, currentPresent + Math.floor(Math.random() * 3) - 1));
        presentElement.textContent = newPresent;
    }
    
    if (lateElement) {
        const currentLate = parseInt(lateElement.textContent);
        // Randomly update late count
        const newLate = Math.max(0, Math.min(8, currentLate + Math.floor(Math.random() * 2) - 1));
        lateElement.textContent = newLate;
    }
}

// Initialize interactive elements
function initializeInteractiveElements() {
    // Add hover effects and tooltips
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// Quick action functions
function addNewEmployee() {
    window.location.href = 'employees.html?action=add';
}

function generateReport() {
    window.location.href = 'reports.html?action=generate';
}

function openUserManagement() {
    window.location.href = 'user-management.html';
}

function openEmployeeManagement() {
    window.location.href = 'employees.html';
}

function openAttendanceManagement() {
    window.location.href = 'attendance.html';
}

function openSystemSettings() {
    window.location.href = 'settings.html';
}

// Profile and settings functions
function viewProfile() {
    // Open profile modal or navigate to profile page
    showNotification('سيتم فتح صفحة الملف الشخصي قريباً', 'info');
}

function openSettings() {
    window.location.href = 'settings.html';
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 90px;
        right: 20px;
        background: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        transform: translateX(400px);
        transition: all 0.3s ease;
        border-right: 4px solid ${getNotificationColor(type)};
    `;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Hide notification
    setTimeout(() => {
        notification.style.transform = 'translateX(400px)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 4000);
}

function getNotificationIcon(type) {
    const icons = {
        success: 'fa-check-circle',
        error: 'fa-exclamation-circle',
        warning: 'fa-exclamation-triangle',
        info: 'fa-info-circle'
    };
    return icons[type] || 'fa-info-circle';
}

function getNotificationColor(type) {
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };
    return colors[type] || '#17a2b8';
}

// Export functions for global access
window.toggleUserMenu = toggleUserMenu;
window.toggleNotifications = toggleNotifications;
window.addNewEmployee = addNewEmployee;
window.generateReport = generateReport;
window.openUserManagement = openUserManagement;
window.openEmployeeManagement = openEmployeeManagement;
window.openAttendanceManagement = openAttendanceManagement;
window.openSystemSettings = openSystemSettings;
window.viewProfile = viewProfile;
window.openSettings = openSettings;
