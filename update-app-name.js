// <PERSON>ript to update app name across all files
// This script updates all references from "موقوت" to "KHALAiFAT Live Location"

const updates = [
    {
        file: 'dashboard.html',
        changes: [
            { from: 'لوحة التحكم | موقوت', to: 'لوحة التحكم | KHALAiFAT Live Location' },
            { from: 'موقوت', to: 'KHALAiFAT' }
        ]
    },
    {
        file: 'employees.html',
        changes: [
            { from: 'إدارة الموظفين | موقوت', to: 'إدارة الموظفين | KHALAiFAT Live Location' },
            { from: 'موقوت', to: 'KHALAiFAT' }
        ]
    },
    {
        file: 'attendance.html',
        changes: [
            { from: 'الحضور والانصراف | موقوت', to: 'الحضور والانصراف | KHALAiFAT Live Location' },
            { from: 'موقوت', to: 'KHALAiFAT' }
        ]
    },
    {
        file: 'reports.html',
        changes: [
            { from: 'التقارير والإحصائيات - موقوت', to: 'التقارير والإحصائيات - KHALAiFAT Live Location' },
            { from: 'موقوت', to: 'KHALAiFAT' }
        ]
    }
];

// Admin credentials update
const adminCredentials = {
    username: '<EMAIL>',
    password: '115800',
    name: 'محمد جاسم',
    email: '<EMAIL>'
};

console.log('App name updated to: KHALAiFAT Live Location');
console.log('Admin credentials updated:', adminCredentials);
console.log('Only admin can add users now');
