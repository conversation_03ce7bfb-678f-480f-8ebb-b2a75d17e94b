<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول | موقوت</title>
    <link rel="icon" type="image/x-icon" href="https://cdn.mawqoot.com/static/images/favicon.ico">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="login-style.css">
</head>

<body>
    <!-- Feedback Modal -->
    <div id="feedback-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>أبدِ رأيك / مساعدة</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <p>تابع بريدك الالكتروني لوصول رد خلال 3 أيام عمل.</p>
                <p>يمكنك أيضا التواصل معنا على <a href="mailto:<EMAIL>"><EMAIL></a>، أو بدء <a
                        href="https://wa.me/message/J6ASVPK655PPJ1" target="_blank">محادثة واتس أب</a>.</p>

                <form id="feedback-form">
                    <div class="form-group">
                        <label for="feedback-email">البريد الإلكتروني</label>
                        <input type="email" id="feedback-email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="feedback-message">الرسالة</label>
                        <textarea id="feedback-message" name="message" rows="4" required></textarea>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" onclick="closeFeedbackModal()">إغلاق</button>
                        <button type="submit" class="btn-primary">إرسال</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="login-container">
        <!-- Left Side - Login Form -->
        <div class="login-form-section">
            <!-- Logo -->
            <div class="logo-container">
                <img src="https://cdn.mawqoot.com/static/images/logo/logo-ar.png" alt="موقوت" class="logo">
            </div>

            <!-- Login Form -->
            <div class="login-form">
                <h1>سجّل الدخول لحسابك</h1>

                <form id="login-form">
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="password">كلمة المرور</label>
                        <div class="password-input">
                            <input type="password" id="password" name="password" required>
                            <button type="button" class="toggle-password" onclick="togglePassword()">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="remember-me">
                            <input type="checkbox" id="remember" name="remember">
                            <span class="checkmark"></span>
                            تذكرني
                        </label>
                        <a href="reset-password.html" class="forgot-password">نسيت كلمة المرور؟</a>
                    </div>

                    <button type="submit" class="login-btn">
                        <span class="btn-text">تسجيل الدخول</span>
                        <div class="loading-spinner" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </button>
                </form>

                <div class="register-link">
                    <p>ليس لديك حساب؟ <a href="register.html">تسجيل حساب منشأة جديد</a></p>
                </div>
            </div>
        </div>

        <!-- Right Side - Background Image -->
        <div class="login-background">
            <div class="background-overlay">
                <div class="background-content">
                    <h2>مرحباً بك في موقوت</h2>
                    <p>النظام الأكثر تطوراً لمتابعة حضور الموظفين وإدارة عمليات الموارد البشرية</p>
                    <div class="features-list">
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>تتبع الحضور والانصراف</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>إدارة الإجازات والمخالفات</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>تقارير مفصلة وإحصائيات</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>واجهة سهلة الاستخدام</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Feedback Button -->
    <button class="feedback-btn" onclick="openFeedbackModal()">
        <i class="fas fa-comment"></i>
        <span>أبدِ رأيك</span>
    </button>

    <!-- JavaScript Notification -->
    <noscript>
        <div class="no-js-warning">
            <p>Your browser does not support JavaScript and this website requires it. Please enable it or change the
                browser.</p>
        </div>
    </noscript>

    <!-- Custom JavaScript -->
    <!-- User Type Selection -->
    <div class="user-type-selection" id="user-type-selection">
        <div class="container">
            <div class="selection-header">
                <h2>اختر نوع المستخدم</h2>
                <p>يرجى اختيار نوع حسابك للمتابعة</p>
            </div>
            <div class="user-types">
                <div class="user-type-card" onclick="selectUserType('admin')">
                    <div class="type-icon admin">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h3>مدير النظام</h3>
                    <p>صلاحيات كاملة لإدارة النظام</p>
                    <ul>
                        <li>إدارة المستخدمين</li>
                        <li>إدارة الموظفين</li>
                        <li>التقارير والإحصائيات</li>
                        <li>إعدادات النظام</li>
                    </ul>
                </div>
                <div class="user-type-card" onclick="selectUserType('supervisor')">
                    <div class="type-icon supervisor">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h3>مشرف</h3>
                    <p>صلاحيات محددة للإشراف</p>
                    <ul>
                        <li>عرض بيانات الموظفين</li>
                        <li>متابعة الحضور</li>
                        <li>التقارير الأساسية</li>
                        <li>إدارة الحضور</li>
                    </ul>
                </div>
                <div class="user-type-card" onclick="selectUserType('employee')">
                    <div class="type-icon employee">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3>موظف</h3>
                    <p>تسجيل الحضور والانصراف</p>
                    <ul>
                        <li>تسجيل الحضور</li>
                        <li>تسجيل الانصراف</li>
                        <li>عرض سجل الحضور</li>
                        <li>تحديث البيانات الشخصية</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Credentials -->
    <div class="demo-credentials" id="demo-credentials">
        <div class="credentials-card">
            <h4>بيانات تجريبية للدخول</h4>
            <div class="credential-item">
                <strong>مدير النظام:</strong>
                <span>admin / 123456</span>
                <button onclick="fillCredentials('admin', '123456')">استخدام</button>
            </div>
            <div class="credential-item">
                <strong>مشرف:</strong>
                <span>supervisor / 123456</span>
                <button onclick="fillCredentials('supervisor', '123456')">استخدام</button>
            </div>
            <div class="credential-item">
                <strong>موظف:</strong>
                <span>employee / 123456</span>
                <button onclick="fillCredentials('employee', '123456')">استخدام</button>
            </div>
        </div>
    </div>

    <script src="auth-system.js"></script>
    <script src="login-script.js"></script>
</body>

</html>