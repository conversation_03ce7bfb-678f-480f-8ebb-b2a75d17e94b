<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام المصادقة - موقوت</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .header p {
            color: #666;
            font-size: 1.2rem;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .test-section h2 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 15px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            justify-content: center;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .test-btn.danger {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
        }
        
        .test-btn.danger:hover {
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
        }
        
        .test-btn.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .test-btn.success:hover {
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }
        
        .status-display {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 1rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: 600;
            color: #333;
        }
        
        .status-value {
            color: #666;
            font-family: monospace;
        }
        
        .links-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }
        
        .link-card {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .link-card:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }
        
        .link-card h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .link-card p {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .link-card a {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .link-card a:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> اختبار نظام المصادقة</h1>
            <p>اختبر جميع وظائف نظام المصادقة والصلاحيات</p>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-user-check"></i> حالة المصادقة الحالية</h2>
            <div class="status-display" id="auth-status">
                <!-- سيتم ملؤها بواسطة JavaScript -->
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-sign-in-alt"></i> اختبار تسجيل الدخول</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testLogin('admin', '123456')">
                    <i class="fas fa-user-shield"></i>
                    تسجيل دخول كمدير
                </button>
                <button class="test-btn" onclick="testLogin('supervisor', '123456')">
                    <i class="fas fa-user-tie"></i>
                    تسجيل دخول كمشرف
                </button>
                <button class="test-btn" onclick="testLogin('employee', '123456')">
                    <i class="fas fa-user"></i>
                    تسجيل دخول كموظف
                </button>
                <button class="test-btn danger" onclick="testLogout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-key"></i> اختبار الصلاحيات</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testPermission('view_employees')">
                    <i class="fas fa-users"></i>
                    اختبار صلاحية الموظفين
                </button>
                <button class="test-btn" onclick="testPermission('view_attendance')">
                    <i class="fas fa-clock"></i>
                    اختبار صلاحية الحضور
                </button>
                <button class="test-btn" onclick="testPermission('view_reports')">
                    <i class="fas fa-chart-bar"></i>
                    اختبار صلاحية التقارير
                </button>
                <button class="test-btn success" onclick="refreshStatus()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث الحالة
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-external-link-alt"></i> روابط سريعة</h2>
            <div class="links-section">
                <div class="link-card">
                    <h3>صفحة تسجيل الدخول</h3>
                    <p>اختبر واجهة تسجيل الدخول مع اختيار نوع المستخدم</p>
                    <a href="login.html">
                        <i class="fas fa-sign-in-alt"></i>
                        فتح صفحة الدخول
                    </a>
                </div>
                <div class="link-card">
                    <h3>لوحة تحكم المدير</h3>
                    <p>لوحة التحكم الكاملة مع جميع الصلاحيات</p>
                    <a href="admin-dashboard.html">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة المدير
                    </a>
                </div>
                <div class="link-card">
                    <h3>لوحة تحكم المشرف</h3>
                    <p>لوحة تحكم محدودة الصلاحيات للمشرفين</p>
                    <a href="supervisor-dashboard.html">
                        <i class="fas fa-user-tie"></i>
                        لوحة المشرف
                    </a>
                </div>
                <div class="link-card">
                    <h3>لوحة تحكم الموظف</h3>
                    <p>واجهة بسيطة لتسجيل الحضور والانصراف</p>
                    <a href="employee-dashboard.html">
                        <i class="fas fa-user"></i>
                        لوحة الموظف
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="auth-system.js"></script>
    <script>
        // تحديث حالة المصادقة
        function updateAuthStatus() {
            const statusDiv = document.getElementById('auth-status');
            const isLoggedIn = authSystem.isLoggedIn();
            
            if (isLoggedIn) {
                const user = authSystem.getCurrentUser();
                statusDiv.innerHTML = `
                    <div class="status-item">
                        <span class="status-label">حالة تسجيل الدخول:</span>
                        <span class="status-value" style="color: #28a745;">✓ مسجل الدخول</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">اسم المستخدم:</span>
                        <span class="status-value">${user.name}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">نوع المستخدم:</span>
                        <span class="status-value">${user.type}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">الصلاحيات:</span>
                        <span class="status-value">${user.permissions.join(', ')}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">لوحة التحكم:</span>
                        <span class="status-value">${authSystem.getDashboardUrl()}</span>
                    </div>
                `;
            } else {
                statusDiv.innerHTML = `
                    <div class="status-item">
                        <span class="status-label">حالة تسجيل الدخول:</span>
                        <span class="status-value" style="color: #dc3545;">✗ غير مسجل الدخول</span>
                    </div>
                `;
            }
        }
        
        // اختبار تسجيل الدخول
        function testLogin(username, password) {
            const result = authSystem.login(username, password);
            if (result.success) {
                alert(`تم تسجيل الدخول بنجاح كـ ${result.user.name}`);
                updateAuthStatus();
            } else {
                alert(`فشل تسجيل الدخول: ${result.message}`);
            }
        }
        
        // اختبار تسجيل الخروج
        function testLogout() {
            authSystem.logout();
            alert('تم تسجيل الخروج بنجاح');
            updateAuthStatus();
        }
        
        // اختبار الصلاحيات
        function testPermission(permission) {
            if (!authSystem.isLoggedIn()) {
                alert('يجب تسجيل الدخول أولاً');
                return;
            }
            
            const hasPermission = authSystem.hasPermission(permission);
            const user = authSystem.getCurrentUser();
            alert(`المستخدم ${user.name} ${hasPermission ? 'لديه' : 'ليس لديه'} صلاحية: ${permission}`);
        }
        
        // تحديث الحالة
        function refreshStatus() {
            updateAuthStatus();
            alert('تم تحديث الحالة');
        }
        
        // تحديث الحالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', updateAuthStatus);
    </script>
</body>
</html>
