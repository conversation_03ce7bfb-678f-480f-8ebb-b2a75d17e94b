<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الطلبات البسيطة - KHALAiFAT Live Location</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="admin-dashboard-style.css">
    
    <script>
        // Check authentication before page loads
        if (!window.authSystem) {
            // Load auth system first
            const script = document.createElement('script');
            script.src = 'auth-system.js';
            script.onload = function() {
                checkAuth();
            };
            document.head.appendChild(script);
        } else {
            checkAuth();
        }
        
        function checkAuth() {
            if (!authSystem.isLoggedIn()) {
                window.location.href = 'login.html';
            }
        }
    </script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-right">
                <div class="logo">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>KHALAiFAT</span>
                </div>
                <div class="breadcrumb">
                    <a href="admin-dashboard.html">لوحة التحكم</a>
                    <span>/</span>
                    <span>الطلبات البسيطة</span>
                </div>
            </div>
            <div class="header-left">
                <button class="notification-btn" onclick="toggleNotifications()">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>
                <div class="user-menu">
                    <div class="user-info" onclick="toggleUserMenu()">
                        <img src="https://via.placeholder.com/40" alt="المستخدم" class="user-avatar">
                        <div class="user-details">
                            <span class="user-name" id="current-user-name">المستخدم</span>
                            <span class="user-role" id="current-user-role">المستخدم</span>
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="user-dropdown" id="user-dropdown">
                        <a href="#" onclick="viewProfile()"><i class="fas fa-user"></i> الملف الشخصي</a>
                        <a href="settings.html"><i class="fas fa-cog"></i> الإعدادات</a>
                        <a href="#" onclick="authSystem.logout()" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <a href="admin-dashboard.html" class="nav-link" id="dashboard-link">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
                <a href="simple-requests.html" class="nav-link active">
                    <i class="fas fa-file-alt"></i>
                    <span>الطلبات البسيطة</span>
                </a>
                <a href="user-management.html" class="nav-link" id="user-management-link">
                    <i class="fas fa-users-cog"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a href="employees.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>الموظفين</span>
                </a>
                <a href="attendance.html" class="nav-link">
                    <i class="fas fa-calendar-check"></i>
                    <span>الحضور</span>
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </a>
                <a href="settings.html" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="page-header">
                <h1><i class="fas fa-file-alt"></i> الطلبات البسيطة</h1>
                <p>إدارة وتتبع الطلبات البسيطة للموظفين</p>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="openNewRequestModal()">
                    <i class="fas fa-plus"></i>
                    طلب جديد
                </button>
                <button class="btn btn-secondary" onclick="exportRequests()">
                    <i class="fas fa-download"></i>
                    تصدير الطلبات
                </button>
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="البحث في الطلبات..." id="search-input" onkeyup="searchRequests()">
                </div>
            </div>

            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <button class="filter-tab active" onclick="filterRequests('all')">
                    <i class="fas fa-list"></i>
                    جميع الطلبات
                    <span class="tab-count" id="all-count">0</span>
                </button>
                <button class="filter-tab" onclick="filterRequests('pending')">
                    <i class="fas fa-clock"></i>
                    قيد الانتظار
                    <span class="tab-count" id="pending-count">0</span>
                </button>
                <button class="filter-tab" onclick="filterRequests('approved')">
                    <i class="fas fa-check-circle"></i>
                    موافق عليها
                    <span class="tab-count" id="approved-count">0</span>
                </button>
                <button class="filter-tab" onclick="filterRequests('rejected')">
                    <i class="fas fa-times-circle"></i>
                    مرفوضة
                    <span class="tab-count" id="rejected-count">0</span>
                </button>
            </div>

            <!-- Requests Table -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-list"></i> قائمة الطلبات</h3>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="data-table" id="requests-table">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>نوع الطلب</th>
                                    <th>مقدم الطلب</th>
                                    <th>تاريخ التقديم</th>
                                    <th>الحالة</th>
                                    <th>الأولوية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="requests-tbody">
                                <!-- Requests will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- New Request Modal -->
    <div id="new-request-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> طلب جديد</h3>
                <span class="close-modal" onclick="closeNewRequestModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="new-request-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="request-type">نوع الطلب *</label>
                            <select id="request-type" required>
                                <option value="">اختر نوع الطلب</option>
                                <option value="leave">طلب إجازة</option>
                                <option value="permission">طلب إذن</option>
                                <option value="overtime">طلب عمل إضافي</option>
                                <option value="equipment">طلب معدات</option>
                                <option value="maintenance">طلب صيانة</option>
                                <option value="training">طلب تدريب</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="priority">الأولوية *</label>
                            <select id="priority" required>
                                <option value="low">منخفضة</option>
                                <option value="medium" selected>متوسطة</option>
                                <option value="high">عالية</option>
                                <option value="urgent">عاجلة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="start-date">تاريخ البداية</label>
                            <input type="date" id="start-date">
                        </div>
                        <div class="form-group">
                            <label for="end-date">تاريخ النهاية</label>
                            <input type="date" id="end-date">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="request-title">عنوان الطلب *</label>
                        <input type="text" id="request-title" placeholder="أدخل عنوان الطلب" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="request-description">وصف الطلب *</label>
                        <textarea id="request-description" rows="4" placeholder="أدخل تفاصيل الطلب..." required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="request-attachment">مرفقات (اختياري)</label>
                        <input type="file" id="request-attachment" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                        <small>يمكن إرفاق ملفات PDF, Word, أو صور</small>
                    </div>
                </form>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeNewRequestModal()">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-primary" onclick="submitNewRequest()">
                    <i class="fas fa-paper-plane"></i>
                    إرسال الطلب
                </button>
            </div>
        </div>
    </div>

    <!-- Request Details Modal -->
    <div id="request-details-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-eye"></i> تفاصيل الطلب</h3>
                <span class="close-modal" onclick="closeRequestDetailsModal()">&times;</span>
            </div>
            <div class="modal-body" id="request-details-content">
                <!-- Request details will be loaded here -->
            </div>
            <div class="form-actions" id="request-actions">
                <!-- Action buttons will be loaded here based on user role -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="auth-system.js"></script>
    <script src="simple-requests-script.js"></script>
</body>
</html>
