// Reports Page JavaScript
let currentDateRange = 'week';
let currentEmployee = 'all';
let currentDepartment = 'all';
let charts = {};

// Sample data for reports
const sampleData = {
    employees: [
        { id: 'emp1', name: 'أحمد محمد', department: 'it', position: 'مطور برمجيات' },
        { id: 'emp2', name: 'فاطمة أحمد', department: 'hr', position: 'أخصائية موارد بشرية' },
        { id: 'emp3', name: 'محمد علي', department: 'finance', position: 'محاسب' },
        { id: 'emp4', name: 'نورا سالم', department: 'marketing', position: 'مسوقة رقمية' }
    ],
    attendance: [
        {
            employee: 'أحمد محمد',
            date: '2024-01-15',
            checkIn: '08:30',
            checkOut: '17:15',
            workingHours: '8:45',
            status: 'present',
            location: 'in-office',
            gpsCompliance: 95
        },
        {
            employee: 'فاطمة أحمد',
            date: '2024-01-15',
            checkIn: '09:15',
            checkOut: '17:30',
            workingHours: '8:15',
            status: 'late',
            location: 'partial',
            gpsCompliance: 78
        },
        {
            employee: 'محمد علي',
            date: '2024-01-15',
            checkIn: '08:45',
            checkOut: '16:45',
            workingHours: '8:00',
            status: 'early-leave',
            location: 'in-office',
            gpsCompliance: 92
        },
        {
            employee: 'نورا سالم',
            date: '2024-01-15',
            checkIn: '--',
            checkOut: '--',
            workingHours: '0:00',
            status: 'absent',
            location: 'out-of-office',
            gpsCompliance: 0
        }
    ],
    violations: [
        { type: 'late', employee: 'فاطمة أحمد', date: '2024-01-15', details: 'تأخير 15 دقيقة' },
        { type: 'location', employee: 'محمد علي', date: '2024-01-14', details: 'خارج نطاق المكتب لمدة 30 دقيقة' },
        { type: 'absence', employee: 'نورا سالم', date: '2024-01-15', details: 'غياب بدون إذن' }
    ]
};

// Initialize page
document.addEventListener('DOMContentLoaded', function () {
    initializeReports();
    loadAttendanceData();
    createCharts();
    updateSummaryCards();
});

// Initialize reports
function initializeReports() {
    console.log('تهيئة صفحة التقارير');

    // Set today's date as default for date inputs
    const today = new Date().toISOString().split('T')[0];
    const startDate = document.getElementById('start-date');
    const endDate = document.getElementById('end-date');

    if (startDate) startDate.value = today;
    if (endDate) endDate.value = today;

    // Initialize tooltips
    initializeTooltips();
}

// Switch between tabs
function switchTab(tabName) {
    // Remove active class from all tabs
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

    // Add active class to selected tab
    document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');
    document.getElementById(`${tabName}-tab`).classList.add('active');

    // Load tab-specific data
    loadTabData(tabName);
}

// Load data for specific tab
function loadTabData(tabName) {
    showLoading();

    setTimeout(() => {
        switch (tabName) {
            case 'attendance':
                loadAttendanceData();
                break;
            case 'gps':
                loadGPSData();
                break;
            case 'performance':
                loadPerformanceData();
                break;
            case 'violations':
                loadViolationsData();
                break;
        }
        hideLoading();
    }, 1000);
}

// Update date range
function updateDateRange() {
    const dateRange = document.getElementById('date-range').value;
    const customDateRange = document.querySelector('.custom-date-range');

    currentDateRange = dateRange;

    if (dateRange === 'custom') {
        customDateRange.style.display = 'flex';
    } else {
        customDateRange.style.display = 'none';
        applyDateRange();
    }
}

// Apply custom date range
function applyCustomDateRange() {
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;

    if (startDate && endDate) {
        console.log(`تطبيق الفترة المخصصة: من ${startDate} إلى ${endDate}`);
        refreshReports();
    }
}

// Apply date range
function applyDateRange() {
    console.log(`تطبيق الفترة الزمنية: ${currentDateRange}`);
    refreshReports();
}

// Filter by employee
function filterByEmployee() {
    currentEmployee = document.getElementById('employee-filter').value;
    console.log(`تصفية حسب الموظف: ${currentEmployee}`);
    refreshReports();
}

// Filter by department
function filterByDepartment() {
    currentDepartment = document.getElementById('department-filter').value;
    console.log(`تصفية حسب القسم: ${currentDepartment}`);
    refreshReports();
}

// Refresh all reports
function refreshReports() {
    showLoading();

    setTimeout(() => {
        updateSummaryCards();
        loadAttendanceData();
        updateCharts();
        hideLoading();
    }, 1500);
}

// Update summary cards
function updateSummaryCards() {
    // Simulate data calculation based on filters
    const totalEmployees = currentDepartment === 'all' ? 24 : Math.floor(Math.random() * 10) + 5;
    const attendanceRate = (Math.random() * 10 + 85).toFixed(1);
    const avgWorkingHours = (Math.random() * 2 + 7).toFixed(1);
    const gpsCompliance = (Math.random() * 15 + 80).toFixed(1);

    document.getElementById('total-employees').textContent = totalEmployees;
    document.getElementById('attendance-rate').textContent = attendanceRate + '%';
    document.getElementById('avg-working-hours').textContent = avgWorkingHours;
    document.getElementById('gps-compliance').textContent = gpsCompliance + '%';
}

// Load attendance data
function loadAttendanceData() {
    const tableBody = document.querySelector('#attendance-table tbody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    sampleData.attendance.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${record.employee}</td>
            <td>${formatDate(record.date)}</td>
            <td>${record.checkIn}</td>
            <td>${record.checkOut}</td>
            <td>${record.workingHours}</td>
            <td><span class="status-badge ${record.status}">${getStatusText(record.status)}</span></td>
            <td><span class="location-badge ${record.location}">${getLocationText(record.location)}</span></td>
        `;
        tableBody.appendChild(row);
    });
}

// Load GPS data
function loadGPSData() {
    console.log('تحميل بيانات GPS');
    // Create GPS-specific charts and maps
    createGPSCharts();
}

// Load performance data
function loadPerformanceData() {
    console.log('تحميل بيانات الأداء');
    createPerformanceCharts();
}

// Load violations data
function loadViolationsData() {
    console.log('تحميل بيانات المخالفات');
    updateViolationsSummary();
}

// Create charts
function createCharts() {
    createDailyAttendanceChart();
    createWorkingHoursChart();
}

// Create daily attendance chart
function createDailyAttendanceChart() {
    const ctx = document.getElementById('dailyAttendanceChart');
    if (!ctx) return;

    charts.dailyAttendance = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
            datasets: [{
                label: 'معدل الحضور',
                data: [95, 92, 88, 94, 96, 90, 85],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function (value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });
}

// Create working hours chart
function createWorkingHoursChart() {
    const ctx = document.getElementById('workingHoursChart');
    if (!ctx) return;

    charts.workingHours = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['8 ساعات', '7-8 ساعات', '6-7 ساعات', 'أقل من 6 ساعات'],
            datasets: [{
                data: [45, 30, 20, 5],
                backgroundColor: [
                    '#28a745',
                    '#667eea',
                    '#ffc107',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Create GPS charts
function createGPSCharts() {
    createOfficePresenceChart();
    initializeLocationMap();
}

// Create office presence chart
function createOfficePresenceChart() {
    const ctx = document.getElementById('officePresenceChart');
    if (!ctx) return;

    if (charts.officePresence) {
        charts.officePresence.destroy();
    }

    charts.officePresence = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['أحمد محمد', 'فاطمة أحمد', 'محمد علي', 'نورا سالم'],
            datasets: [{
                label: 'نسبة التواجد في المكتب',
                data: [95, 78, 92, 0],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#28a745',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function (value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });
}

// Initialize location map
function initializeLocationMap() {
    const mapContainer = document.getElementById('location-map');
    if (!mapContainer) return;

    mapContainer.innerHTML = `
        <div style="text-align: center; color: #666;">
            <i class="fas fa-map-marked-alt" style="font-size: 3rem; margin-bottom: 1rem; color: #667eea;"></i>
            <p>خريطة المواقع</p>
            <p style="font-size: 0.875rem;">سيتم عرض مواقع الموظفين هنا</p>
        </div>
    `;
}

// Create performance charts
function createPerformanceCharts() {
    createMonthlyPerformanceChart();
    createDepartmentComparisonChart();
}

// Create monthly performance chart
function createMonthlyPerformanceChart() {
    const ctx = document.getElementById('monthlyPerformanceChart');
    if (!ctx) return;

    if (charts.monthlyPerformance) {
        charts.monthlyPerformance.destroy();
    }

    charts.monthlyPerformance = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'مؤشر الأداء',
                data: [85, 88, 92, 89, 94, 91],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// Create department comparison chart
function createDepartmentComparisonChart() {
    const ctx = document.getElementById('departmentComparisonChart');
    if (!ctx) return;

    if (charts.departmentComparison) {
        charts.departmentComparison.destroy();
    }

    charts.departmentComparison = new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['الحضور', 'الالتزام بالوقت', 'ساعات العمل', 'الالتزام بالموقع'],
            datasets: [
                {
                    label: 'تقنية المعلومات',
                    data: [95, 92, 88, 94],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.2)'
                },
                {
                    label: 'الموارد البشرية',
                    data: [88, 85, 92, 78],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.2)'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// Update violations summary
function updateViolationsSummary() {
    // Count violations by type
    const violationCounts = {
        late: sampleData.violations.filter(v => v.type === 'late').length,
        absence: sampleData.violations.filter(v => v.type === 'absence').length,
        location: sampleData.violations.filter(v => v.type === 'location').length
    };

    // Update violation cards
    document.querySelectorAll('.violation-count').forEach((element, index) => {
        const types = ['late', 'absence', 'location'];
        element.textContent = violationCounts[types[index]] || 0;
    });
}

// Update charts
function updateCharts() {
    Object.values(charts).forEach(chart => {
        if (chart && chart.update) {
            chart.update();
        }
    });
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function getStatusText(status) {
    const statusMap = {
        'present': 'حاضر',
        'late': 'متأخر',
        'absent': 'غائب',
        'early-leave': 'انصراف مبكر'
    };
    return statusMap[status] || status;
}

function getLocationText(location) {
    const locationMap = {
        'in-office': 'في المكتب',
        'out-of-office': 'خارج المكتب',
        'partial': 'جزئي'
    };
    return locationMap[location] || location;
}

// Pagination functions
function nextPage(tableType) {
    console.log(`الصفحة التالية: ${tableType}`);
}

function previousPage(tableType) {
    console.log(`الصفحة السابقة: ${tableType}`);
}

// Export functions
function exportAllReports() {
    showLoading();
    setTimeout(() => {
        console.log('تصدير جميع التقارير');
        hideLoading();
        showNotification('تم تصدير جميع التقارير بنجاح!', 'success');
    }, 2000);
}

function exportAttendanceReport() {
    console.log('تصدير تقرير الحضور');
    showNotification('تم تصدير تقرير الحضور بنجاح!', 'success');
}

function exportGPSReport() {
    console.log('تصدير تقرير GPS');
    showNotification('تم تصدير تقرير GPS بنجاح!', 'success');
}

function exportPerformanceReport() {
    console.log('تصدير تقرير الأداء');
    showNotification('تم تصدير تقرير الأداء بنجاح!', 'success');
}

function exportViolationsReport() {
    console.log('تصدير تقرير المخالفات');
    showNotification('تم تصدير تقرير المخالفات بنجاح!', 'success');
}

// Loading and notification functions
function showLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.classList.add('active');
    }
}

function hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.classList.remove('active');
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);

    // Hide and remove notification
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
    }, 3000);
}

// Initialize tooltips
function initializeTooltips() {
    // Add tooltip functionality for charts and data points
    console.log('تهيئة التلميحات');
}

// Advanced GPS Analytics
function generateGPSAnalytics() {
    const gpsData = {
        dailyCompliance: [95, 78, 92, 0, 88, 94, 85],
        locationHistory: [
            { employee: 'أحمد محمد', inOfficeTime: 8.5, outOfficeTime: 0.5, compliance: 95 },
            { employee: 'فاطمة أحمد', inOfficeTime: 6.2, outOfficeTime: 2.8, compliance: 78 },
            { employee: 'محمد علي', inOfficeTime: 7.8, outOfficeTime: 1.2, compliance: 92 },
            { employee: 'نورا سالم', inOfficeTime: 0, outOfficeTime: 8, compliance: 0 }
        ],
        violations: [
            { employee: 'فاطمة أحمد', type: 'out-of-office', duration: 45, time: '14:30' },
            { employee: 'محمد علي', type: 'out-of-office', duration: 30, time: '11:15' }
        ]
    };

    return gpsData;
}

// Real-time data simulation
function simulateRealTimeData() {
    setInterval(() => {
        // Update attendance rate
        const currentRate = parseFloat(document.getElementById('attendance-rate').textContent);
        const newRate = (currentRate + (Math.random() - 0.5) * 2).toFixed(1);
        document.getElementById('attendance-rate').textContent = Math.max(0, Math.min(100, newRate)) + '%';

        // Update GPS compliance
        const currentGPS = parseFloat(document.getElementById('gps-compliance').textContent);
        const newGPS = (currentGPS + (Math.random() - 0.5) * 3).toFixed(1);
        document.getElementById('gps-compliance').textContent = Math.max(0, Math.min(100, newGPS)) + '%';

        // Update charts if visible
        updateChartsWithRealTimeData();
    }, 30000); // Update every 30 seconds
}

// Update charts with real-time data
function updateChartsWithRealTimeData() {
    if (charts.dailyAttendance) {
        const newData = charts.dailyAttendance.data.datasets[0].data.map(value =>
            Math.max(0, Math.min(100, value + (Math.random() - 0.5) * 5))
        );
        charts.dailyAttendance.data.datasets[0].data = newData;
        charts.dailyAttendance.update('none');
    }
}

// Advanced filtering
function applyAdvancedFilters() {
    const filters = {
        dateRange: currentDateRange,
        employee: currentEmployee,
        department: currentDepartment,
        status: document.getElementById('status-filter')?.value || 'all',
        location: document.getElementById('location-filter')?.value || 'all',
        minWorkingHours: document.getElementById('min-hours')?.value || 0,
        maxWorkingHours: document.getElementById('max-hours')?.value || 24
    };

    console.log('تطبيق المرشحات المتقدمة:', filters);

    // Filter attendance data
    const filteredData = sampleData.attendance.filter(record => {
        if (filters.employee !== 'all' && !record.employee.includes(filters.employee)) return false;
        if (filters.status !== 'all' && record.status !== filters.status) return false;
        if (filters.location !== 'all' && record.location !== filters.location) return false;

        const workingHours = parseFloat(record.workingHours.split(':')[0]);
        if (workingHours < filters.minWorkingHours || workingHours > filters.maxWorkingHours) return false;

        return true;
    });

    updateTableWithFilteredData(filteredData);
}

// Update table with filtered data
function updateTableWithFilteredData(data) {
    const tableBody = document.querySelector('#attendance-table tbody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    data.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${record.employee}</td>
            <td>${formatDate(record.date)}</td>
            <td>${record.checkIn}</td>
            <td>${record.checkOut}</td>
            <td>${record.workingHours}</td>
            <td><span class="status-badge ${record.status}">${getStatusText(record.status)}</span></td>
            <td>
                <span class="location-badge ${record.location}">${getLocationText(record.location)}</span>
                <small style="display: block; margin-top: 0.25rem; color: #666;">
                    GPS: ${record.gpsCompliance}%
                </small>
            </td>
        `;
        tableBody.appendChild(row);
    });

    // Update pagination info
    const pageInfo = document.getElementById('attendance-page-info');
    if (pageInfo) {
        pageInfo.textContent = `عرض ${data.length} من أصل ${sampleData.attendance.length} سجل`;
    }
}

// Generate detailed reports
function generateDetailedReport(type) {
    const reportData = {
        attendance: generateAttendanceReport(),
        gps: generateGPSReport(),
        performance: generatePerformanceReport(),
        violations: generateViolationsReport()
    };

    return reportData[type];
}

// Generate attendance report
function generateAttendanceReport() {
    const report = {
        summary: {
            totalEmployees: sampleData.employees.length,
            presentToday: sampleData.attendance.filter(r => r.status === 'present').length,
            lateToday: sampleData.attendance.filter(r => r.status === 'late').length,
            absentToday: sampleData.attendance.filter(r => r.status === 'absent').length,
            averageWorkingHours: calculateAverageWorkingHours()
        },
        details: sampleData.attendance.map(record => ({
            ...record,
            workingHoursDecimal: convertTimeToDecimal(record.workingHours),
            efficiency: calculateEfficiency(record)
        })),
        trends: generateAttendanceTrends()
    };

    return report;
}

// Generate GPS report
function generateGPSReport() {
    const gpsAnalytics = generateGPSAnalytics();

    const report = {
        summary: {
            averageCompliance: calculateAverageCompliance(gpsAnalytics.locationHistory),
            totalViolations: gpsAnalytics.violations.length,
            mostCompliantEmployee: getMostCompliantEmployee(gpsAnalytics.locationHistory),
            leastCompliantEmployee: getLeastCompliantEmployee(gpsAnalytics.locationHistory)
        },
        locationAnalysis: gpsAnalytics.locationHistory,
        violations: gpsAnalytics.violations,
        recommendations: generateGPSRecommendations(gpsAnalytics)
    };

    return report;
}

// Generate performance report
function generatePerformanceReport() {
    const report = {
        summary: {
            overallPerformance: 89.5,
            topPerformer: 'أحمد محمد',
            improvementNeeded: ['نورا سالم'],
            departmentRankings: [
                { department: 'تقنية المعلومات', score: 92.5 },
                { department: 'المالية', score: 88.3 },
                { department: 'الموارد البشرية', score: 85.7 },
                { department: 'التسويق', score: 82.1 }
            ]
        },
        individualScores: sampleData.employees.map(emp => ({
            name: emp.name,
            department: emp.department,
            attendanceScore: Math.floor(Math.random() * 20) + 80,
            punctualityScore: Math.floor(Math.random() * 20) + 80,
            locationScore: Math.floor(Math.random() * 20) + 80,
            overallScore: Math.floor(Math.random() * 20) + 80
        }))
    };

    return report;
}

// Generate violations report
function generateViolationsReport() {
    const report = {
        summary: {
            totalViolations: sampleData.violations.length,
            byType: {
                late: sampleData.violations.filter(v => v.type === 'late').length,
                absence: sampleData.violations.filter(v => v.type === 'absence').length,
                location: sampleData.violations.filter(v => v.type === 'location').length
            },
            repeatOffenders: getRepeatOffenders(),
            trend: 'decreasing' // increasing, stable, decreasing
        },
        details: sampleData.violations.map(violation => ({
            ...violation,
            severity: getViolationSeverity(violation),
            actionTaken: getActionTaken(violation)
        })),
        recommendations: generateViolationRecommendations()
    };

    return report;
}

// Utility functions for reports
function calculateAverageWorkingHours() {
    const totalHours = sampleData.attendance.reduce((sum, record) => {
        return sum + convertTimeToDecimal(record.workingHours);
    }, 0);
    return (totalHours / sampleData.attendance.length).toFixed(1);
}

function convertTimeToDecimal(timeString) {
    if (timeString === '0:00' || timeString === '--') return 0;
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours + (minutes / 60);
}

function calculateEfficiency(record) {
    const workingHours = convertTimeToDecimal(record.workingHours);
    const expectedHours = 8;
    return Math.min(100, (workingHours / expectedHours) * 100).toFixed(1);
}

function generateAttendanceTrends() {
    return {
        weeklyTrend: [92, 88, 94, 89, 91, 95, 87],
        monthlyTrend: [89, 91, 88, 92, 94, 90],
        prediction: 'stable'
    };
}

function calculateAverageCompliance(locationHistory) {
    const totalCompliance = locationHistory.reduce((sum, record) => sum + record.compliance, 0);
    return (totalCompliance / locationHistory.length).toFixed(1);
}

function getMostCompliantEmployee(locationHistory) {
    return locationHistory.reduce((max, record) =>
        record.compliance > max.compliance ? record : max
    ).employee;
}

function getLeastCompliantEmployee(locationHistory) {
    return locationHistory.reduce((min, record) =>
        record.compliance < min.compliance ? record : min
    ).employee;
}

function generateGPSRecommendations(gpsData) {
    const recommendations = [];

    gpsData.locationHistory.forEach(record => {
        if (record.compliance < 80) {
            recommendations.push(`يُنصح بمراجعة ${record.employee} حول الالتزام بالموقع المحدد`);
        }
    });

    if (gpsData.violations.length > 5) {
        recommendations.push('يُنصح بتشديد سياسة الالتزام بالموقع');
    }

    return recommendations;
}

function getRepeatOffenders() {
    const offenderCounts = {};
    sampleData.violations.forEach(violation => {
        offenderCounts[violation.employee] = (offenderCounts[violation.employee] || 0) + 1;
    });

    return Object.entries(offenderCounts)
        .filter(([_, count]) => count > 1)
        .map(([employee, count]) => ({ employee, count }));
}

function getViolationSeverity(violation) {
    const severityMap = {
        'late': 'منخفض',
        'absence': 'عالي',
        'location': 'متوسط'
    };
    return severityMap[violation.type] || 'غير محدد';
}

function getActionTaken(violation) {
    const actionMap = {
        'late': 'تنبيه شفهي',
        'absence': 'خصم من الراتب',
        'location': 'تنبيه كتابي'
    };
    return actionMap[violation.type] || 'لم يتم اتخاذ إجراء';
}

function generateViolationRecommendations() {
    return [
        'تطبيق نظام تحفيزي للموظفين الملتزمين',
        'إجراء دورات تدريبية حول أهمية الالتزام بالوقت',
        'مراجعة سياسات الحضور والانصراف',
        'تحسين نظام تتبع الموقع GPS'
    ];
}

// Export to different formats
function exportToExcel(data, filename) {
    console.log(`تصدير إلى Excel: ${filename}`);
    // Simulate Excel export
    showNotification(`تم تصدير ${filename} إلى Excel بنجاح!`, 'success');
}

function exportToPDF(data, filename) {
    console.log(`تصدير إلى PDF: ${filename}`);
    // Simulate PDF export
    showNotification(`تم تصدير ${filename} إلى PDF بنجاح!`, 'success');
}

function exportToCSV(data, filename) {
    console.log(`تصدير إلى CSV: ${filename}`);
    // Simulate CSV export
    showNotification(`تم تصدير ${filename} إلى CSV بنجاح!`, 'success');
}

// Initialize real-time updates
setTimeout(() => {
    simulateRealTimeData();
}, 5000);

console.log('صفحة التقارير والإحصائيات - موقوت');
