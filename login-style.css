/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    background: #f8f9fa;
    min-height: 100vh;
    overflow-x: hidden;
}

/* No JavaScript Warning */
.no-js-warning {
    background: #dc3545;
    color: white;
    padding: 1rem;
    text-align: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
}

/* Main Login Container */
.login-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* Left Side - Login Form */
.login-form-section {
    flex: 1;
    max-width: 500px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: white;
    position: relative;
    z-index: 2;
}

.logo-container {
    text-align: center;
    margin-bottom: 3rem;
}

.logo {
    height: 60px;
    max-width: 200px;
}

.login-form h1 {
    color: #333;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 2rem;
    text-align: center;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #667eea;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
}

.remember-me input {
    width: auto;
    margin-left: 8px;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn.loading .btn-text {
    opacity: 0;
}

.login-btn.loading .loading-spinner {
    display: block !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Register Link */
.register-link {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e9ecef;
}

.register-link p {
    color: #666;
    font-size: 0.9rem;
}

.register-link a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.register-link a:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* Right Side - Background */
.login-background {
    flex: 1;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%),
        url('https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3') center/cover;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.background-overlay {
    background: rgba(0, 0, 0, 0.3);
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.background-content {
    text-align: center;
    color: white;
    max-width: 500px;
}

.background-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.background-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.features-list {
    display: grid;
    gap: 1rem;
    text-align: right;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

.feature-item i {
    color: #4ade80;
    font-size: 1.2rem;
}

/* Feedback Button */
.feedback-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50px;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
}

.feedback-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    color: #333;
    font-weight: 600;
}

.close-modal {
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: #333;
}

.modal-body {
    padding: 1.5rem;
}

.modal-body p {
    margin-bottom: 1rem;
    color: #666;
    line-height: 1.6;
}

.modal-body a {
    color: #667eea;
    text-decoration: none;
}

.modal-body a:hover {
    text-decoration: underline;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.btn-primary,
.btn-secondary {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #e9ecef;
    color: #333;
}

.btn-secondary:hover {
    background: #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        flex-direction: column;
    }

    .login-background {
        order: -1;
        min-height: 200px;
        flex: none;
    }

    .login-form-section {
        max-width: none;
        padding: 1.5rem;
    }

    .background-content h2 {
        font-size: 1.8rem;
    }

    .background-content p {
        font-size: 1rem;
    }

    .features-list {
        display: none;
    }

    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .feedback-btn {
        bottom: 15px;
        right: 15px;
        padding: 10px 16px;
    }

    .feedback-btn span {
        display: none;
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
}

@media (max-width: 480px) {
    .login-form-section {
        padding: 1rem;
    }

    .login-form h1 {
        font-size: 1.5rem;
    }

    .logo {
        height: 50px;
    }

    .background-content h2 {
        font-size: 1.5rem;
    }

    .login-background {
        min-height: 150px;
    }
}

/* Loading Animation */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.fa-spin {
    animation: spin 1s linear infinite;
}

/* Form Validation Styles */
.form-group.error input {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-group.success input {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: none;
}

.form-group.error .error-message {
    display: block;
}

/* Custom Checkbox */
.remember-me {
    position: relative;
    padding-right: 25px;
}

.remember-me input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    top: 0;
    right: 0;
    height: 18px;
    width: 18px;
    background-color: #eee;
    border: 2px solid #ddd;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.remember-me:hover input~.checkmark {
    background-color: #ccc;
}

.remember-me input:checked~.checkmark {
    background-color: #667eea;
    border-color: #667eea;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.remember-me input:checked~.checkmark:after {
    display: block;
}

.remember-me .checkmark:after {
    right: 5px;
    top: 1px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Smooth Transitions */
* {
    transition: all 0.3s ease;
}

input,
button,
.modal {
    transition: all 0.3s ease;
}

/* Focus Styles for Accessibility */
button:focus,
input:focus,
a:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* User Type Selection */
.user-type-selection {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 2rem;
}

.user-type-selection.active {
    display: flex;
}

.selection-header {
    text-align: center;
    margin-bottom: 3rem;
    color: white;
}

.selection-header h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.selection-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.user-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    width: 100%;
}

.user-type-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
    role: button;
    tabindex: 0;
}

.user-type-card:hover,
.user-type-card:focus {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    outline: none;
}

.type-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.type-icon.admin {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.type-icon.supervisor {
    background: linear-gradient(135deg, #4834d4, #686de0);
}

.type-icon.employee {
    background: linear-gradient(135deg, #00d2d3, #54a0ff);
}

.user-type-card h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.user-type-card p {
    color: #666;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.user-type-card ul {
    list-style: none;
    padding: 0;
    text-align: right;
}

.user-type-card li {
    padding: 0.5rem 0;
    color: #555;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
    padding-right: 1.5rem;
}

.user-type-card li:before {
    content: '✓';
    position: absolute;
    right: 0;
    color: #28a745;
    font-weight: bold;
}

.user-type-card li:last-child {
    border-bottom: none;
}

/* Demo Credentials */
.demo-credentials {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1001;
    display: none;
}

.demo-credentials.show {
    display: block;
}

.credentials-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    min-width: 300px;
}

.credentials-card h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    text-align: center;
}

.credential-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.credential-item:last-child {
    border-bottom: none;
}

.credential-item strong {
    color: #333;
    font-size: 0.9rem;
}

.credential-item span {
    color: #666;
    font-family: monospace;
    font-size: 0.85rem;
}

.credential-item button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.credential-item button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Print Styles */
@media print {

    .feedback-btn,
    .login-background,
    .user-type-selection,
    .demo-credentials {
        display: none;
    }

    .login-container {
        flex-direction: column;
    }

    .login-form-section {
        max-width: none;
    }
}