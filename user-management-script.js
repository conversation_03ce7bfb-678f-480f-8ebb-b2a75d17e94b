// User Management Script for KHALAiFAT Live Location
// Only accessible by admin users

document.addEventListener('DOMContentLoaded', function() {
    // Check if user has permission to manage users
    if (!authSystem.canManageUsers()) {
        alert('ليس لديك صلاحية لإدارة المستخدمين');
        window.location.href = authSystem.getDashboardUrl();
        return;
    }
    
    initializeUserManagement();
});

function initializeUserManagement() {
    // Load users data
    loadUsers();
    
    // Setup search functionality
    setupSearch();
    
    // Setup form handlers
    setupFormHandlers();
}

function loadUsers() {
    // In a real application, this would fetch from a server
    const users = authSystem.getAllUsers();
    displayUsers(users);
}

function displayUsers(users) {
    const tbody = document.getElementById('users-table-body');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    users.forEach(user => {
        const row = createUserRow(user);
        tbody.appendChild(row);
    });
}

function createUserRow(user) {
    const row = document.createElement('tr');
    
    const userTypeBadge = getUserTypeBadge(user.type);
    const statusBadge = getStatusBadge(user.active !== false);
    
    row.innerHTML = `
        <td>
            <div class="user-info-cell">
                <img src="${user.avatar || 'https://via.placeholder.com/40'}" alt="${user.name}" class="user-avatar-small">
                <span>${user.name}</span>
            </div>
        </td>
        <td>${user.email}</td>
        <td>${userTypeBadge}</td>
        <td>${user.department || 'غير محدد'}</td>
        <td>${statusBadge}</td>
        <td>${user.createdAt || '2024-01-01'}</td>
        <td>
            <div class="action-buttons-cell">
                <button class="btn-icon" onclick="editUser(${user.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-icon" onclick="viewUserDetails(${user.id})" title="عرض التفاصيل">
                    <i class="fas fa-eye"></i>
                </button>
                ${user.id !== 1 ? `<button class="btn-icon danger" onclick="deleteUser(${user.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>` : ''}
            </div>
        </td>
    `;
    
    return row;
}

function getUserTypeBadge(type) {
    const badges = {
        'admin': '<span class="badge badge-admin">مدير</span>',
        'supervisor': '<span class="badge badge-supervisor">مشرف</span>',
        'employee': '<span class="badge badge-employee">موظف</span>'
    };
    return badges[type] || '<span class="badge">غير محدد</span>';
}

function getStatusBadge(isActive) {
    return isActive 
        ? '<span class="status-badge active">نشط</span>'
        : '<span class="status-badge inactive">غير نشط</span>';
}

function setupSearch() {
    const searchInput = document.getElementById('search-users');
    if (!searchInput) return;
    
    searchInput.addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        const users = authSystem.getAllUsers();
        
        const filteredUsers = users.filter(user => 
            user.name.toLowerCase().includes(searchTerm) ||
            user.email.toLowerCase().includes(searchTerm) ||
            (user.department && user.department.toLowerCase().includes(searchTerm))
        );
        
        displayUsers(filteredUsers);
    });
}

function setupFormHandlers() {
    const addUserForm = document.getElementById('add-user-form');
    if (addUserForm) {
        addUserForm.addEventListener('submit', handleAddUser);
    }
}

function handleAddUser(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const userData = {
        name: formData.get('fullName'),
        email: formData.get('email'),
        password: formData.get('password'),
        type: formData.get('userType'),
        department: formData.get('department'),
        position: formData.get('position'),
        active: true,
        createdAt: new Date().toISOString().split('T')[0]
    };
    
    // Validate required fields
    if (!userData.name || !userData.email || !userData.password || !userData.type) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // Check if email already exists
    if (authSystem.getUserByEmail(userData.email)) {
        showNotification('البريد الإلكتروني مستخدم بالفعل', 'error');
        return;
    }
    
    // Add user
    const success = authSystem.addUser(userData);
    
    if (success) {
        showNotification('تم إضافة المستخدم بنجاح', 'success');
        closeAddUserModal();
        loadUsers(); // Refresh the table
        e.target.reset(); // Clear form
    } else {
        showNotification('حدث خطأ أثناء إضافة المستخدم', 'error');
    }
}

// Modal functions
function openAddUserModal() {
    const modal = document.getElementById('add-user-modal');
    if (modal) {
        modal.style.display = 'block';
    }
}

function closeAddUserModal() {
    const modal = document.getElementById('add-user-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// User actions
function editUser(userId) {
    const user = authSystem.getUserById(userId);
    if (!user) {
        showNotification('المستخدم غير موجود', 'error');
        return;
    }
    
    // For now, show a simple alert
    // In a real application, this would open an edit modal
    showNotification(`سيتم فتح نافذة تعديل المستخدم: ${user.name}`, 'info');
}

function viewUserDetails(userId) {
    const user = authSystem.getUserById(userId);
    if (!user) {
        showNotification('المستخدم غير موجود', 'error');
        return;
    }
    
    // Show user details
    const details = `
        الاسم: ${user.name}
        البريد الإلكتروني: ${user.email}
        النوع: ${getUserTypeText(user.type)}
        القسم: ${user.department || 'غير محدد'}
        المنصب: ${user.position || 'غير محدد'}
    `;
    
    alert(details);
}

function deleteUser(userId) {
    const user = authSystem.getUserById(userId);
    if (!user) {
        showNotification('المستخدم غير موجود', 'error');
        return;
    }
    
    if (user.id === 1) {
        showNotification('لا يمكن حذف المدير الرئيسي', 'error');
        return;
    }
    
    if (confirm(`هل أنت متأكد من حذف المستخدم: ${user.name}؟`)) {
        const success = authSystem.deleteUser(userId);
        
        if (success) {
            showNotification('تم حذف المستخدم بنجاح', 'success');
            loadUsers(); // Refresh the table
        } else {
            showNotification('حدث خطأ أثناء حذف المستخدم', 'error');
        }
    }
}

function exportUsers() {
    const users = authSystem.getAllUsers();
    const csvContent = generateCSV(users);
    downloadCSV(csvContent, 'users-export.csv');
    showNotification('تم تصدير قائمة المستخدمين', 'success');
}

function generateCSV(users) {
    const headers = ['الاسم', 'البريد الإلكتروني', 'النوع', 'القسم', 'المنصب', 'الحالة'];
    const rows = users.map(user => [
        user.name,
        user.email,
        getUserTypeText(user.type),
        user.department || '',
        user.position || '',
        user.active !== false ? 'نشط' : 'غير نشط'
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function getUserTypeText(type) {
    const types = {
        'admin': 'مدير',
        'supervisor': 'مشرف',
        'employee': 'موظف'
    };
    return types[type] || 'غير محدد';
}

// Header functions
function toggleUserMenu() {
    const dropdown = document.getElementById('user-dropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

function toggleNotifications() {
    showNotification('سيتم تطوير نظام الإشعارات قريباً', 'info');
}

function viewProfile() {
    showNotification('سيتم فتح صفحة الملف الشخصي قريباً', 'info');
}

function openSettings() {
    window.location.href = 'settings.html';
}

// Notification system
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        border-radius: 8px;
        padding: 1rem;
        max-width: 400px;
        z-index: 10000;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        animation: slideInRight 0.3s ease;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Close modal when clicking outside
window.addEventListener('click', function(e) {
    const modal = document.getElementById('add-user-modal');
    if (e.target === modal) {
        closeAddUserModal();
    }
});

// Export functions for global access
window.openAddUserModal = openAddUserModal;
window.closeAddUserModal = closeAddUserModal;
window.editUser = editUser;
window.viewUserDetails = viewUserDetails;
window.deleteUser = deleteUser;
window.exportUsers = exportUsers;
window.toggleUserMenu = toggleUserMenu;
window.toggleNotifications = toggleNotifications;
window.viewProfile = viewProfile;
window.openSettings = openSettings;
