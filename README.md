# موقوت - صفحة الهبوط

صفحة هبوط احترافية لنظام موقوت لإدارة حضور الموظفين، مطابقة للتصميم الأصلي للموقع.

## المميزات

### التصميم
- تصميم متجاوب (Responsive) يعمل على جميع الأجهزة
- واجهة مستخدم عصرية وجذابة
- ألوان متدرجة وتأثيرات بصرية احترافية
- خطوط عربية واضحة ومقروءة (Cairo Font)

### الأقسام الرئيسية
1. **الهيدر والتنقل** - شريط علوي ثابت مع قائمة تنقل
2. **القسم الرئيسي** - عنوان جذاب مع أزرار الإجراءات
3. **الخصائص** - عرض مفصل لمميزات النظام:
   - حضور الموظفين
   - كشف المخالفات
   - إدارة الإجازات
   - الرواتب والالتزامات المالية
4. **الفيديو التعريفي** - قسم لعرض الفيديو التوضيحي
5. **كيف ابدأ؟** - خطوات البدء بالنظام
6. **تحميل التطبيق** - روابط تحميل التطبيقات
7. **لماذا موقوت؟** - مميزات النظام
8. **الأسعار** - جداول الأسعار مع حاسبة تفاعلية
9. **شركاء النجاح** - شعارات الشركاء
10. **آراء المستخدمين** - شهادات العملاء
11. **الأسئلة الشائعة** - أكورديون تفاعلي للأسئلة
12. **اتصل بنا** - معلومات التواصل
13. **الفوتر** - روابط مفيدة ومعلومات إضافية

### التفاعلات والوظائف
- **قائمة تنقل متجاوبة** - تتحول لقائمة هامبرغر في الشاشات الصغيرة
- **حاسبة الأسعار** - حساب تلقائي للتكلفة حسب عدد الموظفين
- **مبدل الأسعار** - التبديل بين الأسعار الشهرية والسنوية
- **أكورديون الأسئلة** - فتح وإغلاق الأسئلة الشائعة
- **تمرير سلس** - انتقال سلس بين الأقسام
- **تأثيرات التحميل** - رسوم متحركة عند ظهور العناصر
- **إخفاء الهيدر** - إخفاء الهيدر عند التمرير لأسفل

### التقنيات المستخدمة
- **HTML5** - هيكل الصفحة
- **CSS3** - التصميم والتنسيق
  - CSS Grid & Flexbox للتخطيط
  - CSS Animations للحركات
  - Media Queries للاستجابة
- **JavaScript** - التفاعلات والوظائف
  - Intersection Observer API
  - Event Listeners
  - DOM Manipulation

## هيكل الملفات

```
موقوت/
├── index.html          # الصفحة الرئيسية
├── style.css           # ملف التصميم
├── script.js           # ملف JavaScript
└── README.md           # ملف التوثيق
```

## كيفية الاستخدام

1. **فتح الموقع**: افتح ملف `index.html` في أي متصفح ويب
2. **التصفح**: استخدم قائمة التنقل للانتقال بين الأقسام
3. **التفاعل**: جرب الحاسبة والأسئلة الشائعة والمبدلات

## التخصيص

### تغيير الألوان
يمكن تعديل الألوان الأساسية في ملف `style.css`:
```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --text-color: #333;
    --bg-color: #f8f9fa;
}
```

### إضافة محتوى جديد
- أضف أقسام جديدة في ملف `index.html`
- أضف التصميم المناسب في `style.css`
- أضف التفاعلات في `script.js`

### تعديل الصور
استبدل روابط الصور في ملف HTML بصور محلية أو روابط جديدة.

## المتطلبات

- متصفح ويب حديث يدعم:
  - CSS Grid
  - Flexbox
  - ES6 JavaScript
  - Intersection Observer API

## التوافق

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ الأجهزة المحمولة (iOS/Android)

## الأداء

- **سرعة التحميل**: محسن للتحميل السريع
- **SEO**: محسن لمحركات البحث
- **إمكانية الوصول**: يدعم قارئات الشاشة
- **استجابة**: يعمل بسلاسة على جميع أحجام الشاشات

## المساهمة

لتحسين الموقع:
1. أضف مميزات جديدة
2. حسن الأداء
3. أصلح الأخطاء
4. حسن التصميم

## الترخيص

هذا المشروع مخصص للاستخدام التعليمي والتطويري.

## التواصل

للاستفسارات والدعم:
- البريد الإلكتروني: <EMAIL>
- الموقع الرسمي: https://www.mawqoot.com

---

تم إنشاء هذا المشروع كنسخة مطابقة لموقع موقوت الأصلي باستخدام HTML, CSS, و JavaScript خالص.
