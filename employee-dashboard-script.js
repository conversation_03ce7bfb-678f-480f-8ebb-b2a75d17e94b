// Employee Dashboard JavaScript
let currentLocation = null;
let locationWatchId = null;
let attendanceData = {
    checkInTime: null,
    checkOutTime: null,
    isCheckedIn: false,
    totalHours: 0
};

document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    if (!authSystem.isLoggedIn()) {
        window.location.href = 'login.html';
        return;
    }

    const currentUser = authSystem.getCurrentUser();
    if (currentUser.type !== authSystem.userTypes.EMPLOYEE) {
        window.location.href = authSystem.getDashboardUrl();
        return;
    }

    // Initialize dashboard
    initializeDashboard();
    updateUserInfo();
    updateCurrentTime();
    updateCurrentDate();
    loadAttendanceData();
    requestLocationAccess();
    
    // Set up real-time updates
    setInterval(updateCurrentTime, 1000);
    setInterval(updateWorkingHours, 60000); // Update every minute
});

// Initialize dashboard
function initializeDashboard() {
    setupEventListeners();
    loadWeeklyStats();
    loadRecentActivity();
}

// Setup event listeners
function setupEventListeners() {
    // User menu toggle
    document.addEventListener('click', function(e) {
        const userMenu = document.getElementById('user-dropdown');
        const userInfo = document.querySelector('.user-info');
        
        if (!userInfo.contains(e.target)) {
            userMenu.classList.remove('show');
        }
    });
}

// Update user info
function updateUserInfo() {
    const currentUser = authSystem.getCurrentUser();
    const userNameElement = document.querySelector('.user-name');
    const welcomeTitle = document.querySelector('.welcome-content h1');
    
    if (userNameElement) userNameElement.textContent = currentUser.name;
    if (welcomeTitle) welcomeTitle.textContent = `مرحباً، ${currentUser.name}`;
}

// Update current time
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    });
    
    const timeDisplay = document.getElementById('time-display');
    if (timeDisplay) {
        timeDisplay.textContent = timeString;
    }
}

// Update current date
function updateCurrentDate() {
    const now = new Date();
    const dateString = now.toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    const dateElement = document.getElementById('current-date');
    if (dateElement) {
        dateElement.textContent = dateString;
    }
}

// Request location access
function requestLocationAccess() {
    if (!navigator.geolocation) {
        showNotification('المتصفح لا يدعم خدمات الموقع', 'error');
        return;
    }
    
    // Check if location permission was previously granted
    navigator.permissions.query({name: 'geolocation'}).then(function(result) {
        if (result.state === 'granted') {
            startLocationTracking();
        } else if (result.state === 'prompt') {
            showLocationModal();
        } else {
            showNotification('تم رفض إذن الموقع. يرجى تفعيله من إعدادات المتصفح', 'warning');
        }
    });
}

// Show location permission modal
function showLocationModal() {
    const modal = document.getElementById('location-modal');
    modal.classList.add('show');
}

// Close location modal
function closeLocationModal() {
    const modal = document.getElementById('location-modal');
    modal.classList.remove('show');
}

// Request location permission
function requestLocationPermission() {
    navigator.geolocation.getCurrentPosition(
        function(position) {
            closeLocationModal();
            startLocationTracking();
            showNotification('تم تفعيل تتبع الموقع بنجاح', 'success');
        },
        function(error) {
            closeLocationModal();
            handleLocationError(error);
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000
        }
    );
}

// Start location tracking
function startLocationTracking() {
    if (navigator.geolocation) {
        locationWatchId = navigator.geolocation.watchPosition(
            updateLocation,
            handleLocationError,
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    }
}

// Update location
function updateLocation(position) {
    currentLocation = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        timestamp: new Date()
    };
    
    // Update location display
    updateLocationDisplay();
}

// Handle location errors
function handleLocationError(error) {
    let message = 'حدث خطأ في تحديد الموقع';
    
    switch(error.code) {
        case error.PERMISSION_DENIED:
            message = 'تم رفض إذن الوصول للموقع';
            break;
        case error.POSITION_UNAVAILABLE:
            message = 'معلومات الموقع غير متاحة';
            break;
        case error.TIMEOUT:
            message = 'انتهت مهلة تحديد الموقع';
            break;
    }
    
    showNotification(message, 'error');
    
    const locationElement = document.getElementById('current-location');
    if (locationElement) {
        locationElement.textContent = 'غير متاح';
    }
}

// Update location display
function updateLocationDisplay() {
    const locationElement = document.getElementById('current-location');
    if (locationElement && currentLocation) {
        // In a real app, you would reverse geocode the coordinates
        locationElement.textContent = `${currentLocation.latitude.toFixed(4)}, ${currentLocation.longitude.toFixed(4)}`;
    }
}

// Load attendance data
function loadAttendanceData() {
    // Load from localStorage or API
    const savedData = localStorage.getItem('attendanceData');
    if (savedData) {
        attendanceData = JSON.parse(savedData);
        updateAttendanceUI();
    }
    
    updateAttendanceStatus();
}

// Update attendance status
function updateAttendanceStatus() {
    const statusIndicator = document.getElementById('status-indicator');
    const statusText = document.getElementById('status-text');
    
    if (!statusIndicator || !statusText) return;
    
    if (attendanceData.isCheckedIn && !attendanceData.checkOutTime) {
        statusIndicator.className = 'status-indicator checked-in';
        statusText.textContent = 'تم تسجيل الحضور';
    } else if (attendanceData.checkOutTime) {
        statusIndicator.className = 'status-indicator checked-out';
        statusText.textContent = 'تم تسجيل الانصراف';
    } else {
        statusIndicator.className = 'status-indicator not-checked-in';
        statusText.textContent = 'لم يتم تسجيل الحضور';
    }
}

// Update attendance UI
function updateAttendanceUI() {
    const checkInBtn = document.getElementById('check-in-btn');
    const checkOutBtn = document.getElementById('check-out-btn');
    const checkInCard = document.getElementById('check-in-card');
    const checkOutCard = document.getElementById('check-out-card');
    
    if (attendanceData.isCheckedIn && !attendanceData.checkOutTime) {
        // Checked in, enable check out
        checkInBtn.disabled = true;
        checkOutBtn.disabled = false;
        checkInCard.classList.add('disabled');
        checkOutCard.classList.remove('disabled');
    } else if (attendanceData.checkOutTime) {
        // Checked out, disable both
        checkInBtn.disabled = true;
        checkOutBtn.disabled = true;
        checkInCard.classList.add('disabled');
        checkOutCard.classList.add('disabled');
    } else {
        // Not checked in, enable check in
        checkInBtn.disabled = false;
        checkOutBtn.disabled = true;
        checkInCard.classList.remove('disabled');
        checkOutCard.classList.add('disabled');
    }
    
    // Update time displays
    const checkInTimeElement = document.getElementById('check-in-time');
    const checkOutTimeElement = document.getElementById('check-out-time');
    const totalHoursElement = document.getElementById('total-hours');
    
    if (checkInTimeElement) {
        checkInTimeElement.textContent = attendanceData.checkInTime || '--:--';
    }
    
    if (checkOutTimeElement) {
        checkOutTimeElement.textContent = attendanceData.checkOutTime || '--:--';
    }
    
    if (totalHoursElement) {
        totalHoursElement.textContent = attendanceData.totalHours > 0 ? 
            `${attendanceData.totalHours.toFixed(1)} ساعة` : '0 ساعة';
    }
}

// Check in function
function checkIn() {
    if (!currentLocation) {
        showNotification('يرجى تفعيل خدمات الموقع أولاً', 'warning');
        return;
    }
    
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    });
    
    attendanceData.checkInTime = timeString;
    attendanceData.isCheckedIn = true;
    attendanceData.checkInTimestamp = now.getTime();
    attendanceData.checkInLocation = currentLocation;
    
    // Save to localStorage
    localStorage.setItem('attendanceData', JSON.stringify(attendanceData));
    
    updateAttendanceUI();
    updateAttendanceStatus();
    
    showNotification(`تم تسجيل الحضور بنجاح في ${timeString}`, 'success');
    
    // Start continuous location tracking during work hours
    if (!locationWatchId) {
        startLocationTracking();
    }
}

// Check out function
function checkOut() {
    if (!attendanceData.isCheckedIn) {
        showNotification('يجب تسجيل الحضور أولاً', 'warning');
        return;
    }
    
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    });
    
    attendanceData.checkOutTime = timeString;
    attendanceData.checkOutTimestamp = now.getTime();
    attendanceData.checkOutLocation = currentLocation;
    
    // Calculate total hours
    if (attendanceData.checkInTimestamp) {
        const workingMilliseconds = now.getTime() - attendanceData.checkInTimestamp;
        attendanceData.totalHours = workingMilliseconds / (1000 * 60 * 60);
    }
    
    // Save to localStorage
    localStorage.setItem('attendanceData', JSON.stringify(attendanceData));
    
    updateAttendanceUI();
    updateAttendanceStatus();
    
    showNotification(`تم تسجيل الانصراف بنجاح في ${timeString}`, 'success');
    
    // Stop location tracking
    if (locationWatchId) {
        navigator.geolocation.clearWatch(locationWatchId);
        locationWatchId = null;
    }
}

// Update working hours in real-time
function updateWorkingHours() {
    if (attendanceData.isCheckedIn && !attendanceData.checkOutTime && attendanceData.checkInTimestamp) {
        const now = new Date();
        const workingMilliseconds = now.getTime() - attendanceData.checkInTimestamp;
        const currentHours = workingMilliseconds / (1000 * 60 * 60);
        
        const totalHoursElement = document.getElementById('total-hours');
        if (totalHoursElement) {
            totalHoursElement.textContent = `${currentHours.toFixed(1)} ساعة`;
        }
    }
}

// Load weekly statistics
function loadWeeklyStats() {
    // Simulate loading weekly data
    const weeklyData = {
        daysPresent: 4,
        lateDays: 1,
        absentDays: 0,
        totalWeeklyHours: 32
    };
    
    document.getElementById('days-present').textContent = weeklyData.daysPresent;
    document.getElementById('late-days').textContent = weeklyData.lateDays;
    document.getElementById('absent-days').textContent = weeklyData.absentDays;
    document.getElementById('total-weekly-hours').textContent = weeklyData.totalWeeklyHours;
}

// Load recent activity
function loadRecentActivity() {
    // Recent activity is already in HTML, but you could load it dynamically here
    console.log('Recent activity loaded');
}

// Toggle user menu
function toggleUserMenu() {
    const dropdown = document.getElementById('user-dropdown');
    dropdown.classList.toggle('show');
}

// View profile
function viewProfile() {
    showNotification('سيتم فتح صفحة الملف الشخصي قريباً', 'info');
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 90px;
        right: 20px;
        background: white;
        padding: 1rem 1.5rem;
        border-radius: 25px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        transform: translateX(400px);
        transition: all 0.3s ease;
        border-right: 4px solid ${getNotificationColor(type)};
        backdrop-filter: blur(10px);
    `;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Hide notification
    setTimeout(() => {
        notification.style.transform = 'translateX(400px)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 4000);
}

function getNotificationIcon(type) {
    const icons = {
        success: 'fa-check-circle',
        error: 'fa-exclamation-circle',
        warning: 'fa-exclamation-triangle',
        info: 'fa-info-circle'
    };
    return icons[type] || 'fa-info-circle';
}

function getNotificationColor(type) {
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };
    return colors[type] || '#17a2b8';
}

// Export functions for global access
window.toggleUserMenu = toggleUserMenu;
window.checkIn = checkIn;
window.checkOut = checkOut;
window.viewProfile = viewProfile;
window.requestLocationPermission = requestLocationPermission;
window.closeLocationModal = closeLocationModal;
