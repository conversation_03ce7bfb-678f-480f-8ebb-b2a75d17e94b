<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - KHALAiFAT Live Location</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="admin-dashboard-style.css">
    
    <script>
        // Check authentication before page loads
        if (!window.authSystem) {
            // Load auth system first
            const script = document.createElement('script');
            script.src = 'auth-system.js';
            script.onload = function() {
                checkAuth();
            };
            document.head.appendChild(script);
        } else {
            checkAuth();
        }
        
        function checkAuth() {
            if (!authSystem.isLoggedIn()) {
                window.location.href = 'login.html';
            }
        }
    </script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-right">
                <div class="logo">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>KHALAiFAT</span>
                </div>
                <div class="breadcrumb">
                    <a href="admin-dashboard.html">لوحة التحكم</a>
                    <span>/</span>
                    <span>الإعدادات</span>
                </div>
            </div>
            <div class="header-left">
                <button class="notification-btn" onclick="toggleNotifications()">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">2</span>
                </button>
                <div class="user-menu">
                    <div class="user-info" onclick="toggleUserMenu()">
                        <img src="https://via.placeholder.com/40" alt="المستخدم" class="user-avatar">
                        <div class="user-details">
                            <span class="user-name" id="current-user-name">المستخدم</span>
                            <span class="user-role" id="current-user-role">المستخدم</span>
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="user-dropdown" id="user-dropdown">
                        <a href="#" onclick="viewProfile()"><i class="fas fa-user"></i> الملف الشخصي</a>
                        <a href="#" onclick="openSettings()"><i class="fas fa-cog"></i> الإعدادات</a>
                        <a href="#" onclick="authSystem.logout()" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <a href="admin-dashboard.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
                <a href="user-management.html" class="nav-link" id="user-management-link">
                    <i class="fas fa-users-cog"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a href="employees.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>الموظفين</span>
                </a>
                <a href="attendance.html" class="nav-link">
                    <i class="fas fa-calendar-check"></i>
                    <span>الحضور</span>
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </a>
                <a href="settings.html" class="nav-link active">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="page-header">
                <h1>إعدادات النظام</h1>
                <p>إدارة إعدادات النظام والتفضيلات</p>
            </div>

            <!-- Settings Sections -->
            <div class="settings-container">
                <!-- General Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-cog"></i> الإعدادات العامة</h3>
                    </div>
                    <div class="card-body">
                        <div class="settings-section">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>اسم النظام</h4>
                                    <p>اسم النظام الذي يظهر في جميع الصفحات</p>
                                </div>
                                <div class="setting-control">
                                    <input type="text" value="KHALAiFAT Live Location" readonly>
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>المنطقة الزمنية</h4>
                                    <p>المنطقة الزمنية المستخدمة في النظام</p>
                                </div>
                                <div class="setting-control">
                                    <select>
                                        <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                                        <option value="Asia/Dubai">دبي (GMT+4)</option>
                                        <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>اللغة الافتراضية</h4>
                                    <p>لغة واجهة النظام</p>
                                </div>
                                <div class="setting-control">
                                    <select>
                                        <option value="ar" selected>العربية</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Attendance Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-calendar-check"></i> إعدادات الحضور</h3>
                    </div>
                    <div class="card-body">
                        <div class="settings-section">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>ساعات العمل اليومية</h4>
                                    <p>عدد ساعات العمل المطلوبة يومياً</p>
                                </div>
                                <div class="setting-control">
                                    <input type="number" value="8" min="1" max="24">
                                    <span>ساعة</span>
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>وقت بداية العمل</h4>
                                    <p>الوقت الافتراضي لبداية العمل</p>
                                </div>
                                <div class="setting-control">
                                    <input type="time" value="08:00">
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>وقت انتهاء العمل</h4>
                                    <p>الوقت الافتراضي لانتهاء العمل</p>
                                </div>
                                <div class="setting-control">
                                    <input type="time" value="17:00">
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>تتبع الموقع الجغرافي</h4>
                                    <p>تفعيل تتبع موقع الموظفين أثناء العمل</p>
                                </div>
                                <div class="setting-control">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-shield-alt"></i> إعدادات الأمان</h3>
                    </div>
                    <div class="card-body">
                        <div class="settings-section">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>مدة انتهاء الجلسة</h4>
                                    <p>المدة بالدقائق قبل انتهاء جلسة المستخدم</p>
                                </div>
                                <div class="setting-control">
                                    <input type="number" value="60" min="15" max="480">
                                    <span>دقيقة</span>
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>تسجيل العمليات</h4>
                                    <p>حفظ سجل بجميع العمليات في النظام</p>
                                </div>
                                <div class="setting-control">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>التحقق بخطوتين</h4>
                                    <p>تفعيل التحقق بخطوتين لحسابات المديرين</p>
                                </div>
                                <div class="setting-control">
                                    <label class="switch">
                                        <input type="checkbox">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notification Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-bell"></i> إعدادات الإشعارات</h3>
                    </div>
                    <div class="card-body">
                        <div class="settings-section">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>إشعارات التأخير</h4>
                                    <p>إرسال إشعار عند تأخر الموظف</p>
                                </div>
                                <div class="setting-control">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>إشعارات الغياب</h4>
                                    <p>إرسال إشعار عند غياب الموظف</p>
                                </div>
                                <div class="setting-control">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>التقارير الأسبوعية</h4>
                                    <p>إرسال تقرير أسبوعي بالحضور والغياب</p>
                                </div>
                                <div class="setting-control">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="settings-actions">
                <button class="btn btn-primary" onclick="saveSettings()">
                    <i class="fas fa-save"></i>
                    حفظ الإعدادات
                </button>
                <button class="btn btn-secondary" onclick="resetSettings()">
                    <i class="fas fa-undo"></i>
                    إعادة تعيين
                </button>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="auth-system.js"></script>
    <script src="settings-script.js"></script>
</body>
</html>
