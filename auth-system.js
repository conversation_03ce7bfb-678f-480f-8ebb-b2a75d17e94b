// Authentication and Authorization System
class AuthSystem {
    constructor() {
        this.currentUser = null;
        this.userTypes = {
            ADMIN: 'admin',
            SUPERVISOR: 'supervisor',
            EMPLOYEE: 'employee'
        };

        // Sample users database
        this.users = [
            {
                id: 1,
                username: 'mohd.jass<PERSON>@khalaifat.com',
                password: '115800',
                name: 'محمد جاسم',
                email: 'mohd.jass<PERSON>@khalaifat.com',
                type: this.userTypes.ADMIN,
                department: 'الإدارة العامة',
                position: 'مدير عام',
                avatar: 'https://via.placeholder.com/100',
                permissions: ['all', 'add_users', 'manage_users']
            },
            {
                id: 2,
                username: 'supervisor',
                password: '123456',
                name: 'فاطمة المشرفة',
                email: '<EMAIL>',
                type: this.userTypes.SUPERVISOR,
                department: 'الموارد البشرية',
                position: 'مشرفة موارد بشرية',
                avatar: 'https://via.placeholder.com/100',
                permissions: ['view_employees', 'view_attendance', 'view_reports', 'manage_attendance']
            },
            {
                id: 3,
                username: 'employee',
                password: '123456',
                name: 'محمد الموظف',
                email: '<EMAIL>',
                type: this.userTypes.EMPLOYEE,
                department: 'تقنية المعلومات',
                position: 'مطور برمجيات',
                avatar: 'https://via.placeholder.com/100',
                permissions: ['attendance_only']
            }
        ];

        // Load users from localStorage if available
        this.loadUsers();

        // Load current user from localStorage
        this.loadCurrentUser();
    }

    // Login function
    login(username, password) {
        const user = this.users.find(u => u.username === username && u.password === password);

        if (user) {
            this.currentUser = user;
            localStorage.setItem('currentUser', JSON.stringify(user));
            localStorage.setItem('loginTime', new Date().toISOString());
            return { success: true, user: user };
        }

        return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
    }

    // Logout function
    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        localStorage.removeItem('loginTime');
        window.location.href = 'login.html';
    }

    // Load current user from localStorage
    loadCurrentUser() {
        const userData = localStorage.getItem('currentUser');
        if (userData) {
            this.currentUser = JSON.parse(userData);
        }
    }

    // Check if user is logged in
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Check user permissions
    hasPermission(permission) {
        if (!this.currentUser) return false;

        // Admin has all permissions
        if (this.currentUser.type === this.userTypes.ADMIN) {
            return true;
        }

        return this.currentUser.permissions.includes(permission);
    }

    // Check if user can add new users (Admin only)
    canAddUsers() {
        if (!this.isLoggedIn()) return false;

        const user = this.getCurrentUser();
        if (!user) return false;

        // Only admin can add users
        return user.type === this.userTypes.ADMIN &&
            (user.permissions.includes('all') || user.permissions.includes('add_users'));
    }

    // Check if user can manage users (Admin only)
    canManageUsers() {
        if (!this.isLoggedIn()) return false;

        const user = this.getCurrentUser();
        if (!user) return false;

        // Only admin can manage users
        return user.type === this.userTypes.ADMIN &&
            (user.permissions.includes('all') || user.permissions.includes('manage_users'));
    }

    // Check if user can access page
    canAccessPage(pageName) {
        if (!this.isLoggedIn()) return false;

        const userType = this.currentUser.type;

        switch (pageName) {
            case 'dashboard':
                return true; // All users can access dashboard

            case 'employees':
                return userType === this.userTypes.ADMIN ||
                    this.hasPermission('view_employees');

            case 'attendance':
                return true; // All users can access attendance (but with different views)

            case 'reports':
                return userType === this.userTypes.ADMIN ||
                    this.hasPermission('view_reports');

            case 'settings':
                return userType === this.userTypes.ADMIN;

            case 'user-management':
                return userType === this.userTypes.ADMIN;

            default:
                return false;
        }
    }

    // Get user dashboard URL based on type
    getDashboardUrl() {
        if (!this.currentUser) return 'login.html';

        switch (this.currentUser.type) {
            case this.userTypes.ADMIN:
                return 'admin-dashboard.html';
            case this.userTypes.SUPERVISOR:
                return 'supervisor-dashboard.html';
            case this.userTypes.EMPLOYEE:
                return 'employee-dashboard.html';
            default:
                return 'login.html';
        }
    }

    // Get navigation menu based on user type
    getNavigationMenu() {
        if (!this.currentUser) return [];

        const baseMenu = [
            {
                name: 'الرئيسية',
                icon: 'fas fa-home',
                url: this.getDashboardUrl(),
                permission: true
            }
        ];

        const adminMenu = [
            ...baseMenu,
            {
                name: 'إدارة المستخدمين',
                icon: 'fas fa-users-cog',
                url: 'user-management.html',
                permission: this.canAccessPage('user-management')
            },
            {
                name: 'الموظفين',
                icon: 'fas fa-users',
                url: 'employees.html',
                permission: this.canAccessPage('employees')
            },
            {
                name: 'الحضور والانصراف',
                icon: 'fas fa-calendar-check',
                url: 'attendance.html',
                permission: this.canAccessPage('attendance')
            },
            {
                name: 'التقارير',
                icon: 'fas fa-chart-bar',
                url: 'reports.html',
                permission: this.canAccessPage('reports')
            },
            {
                name: 'الإعدادات',
                icon: 'fas fa-cog',
                url: 'settings.html',
                permission: this.canAccessPage('settings')
            }
        ];

        const supervisorMenu = [
            ...baseMenu,
            {
                name: 'الموظفين',
                icon: 'fas fa-users',
                url: 'employees.html',
                permission: this.canAccessPage('employees')
            },
            {
                name: 'الحضور والانصراف',
                icon: 'fas fa-calendar-check',
                url: 'attendance.html',
                permission: this.canAccessPage('attendance')
            },
            {
                name: 'التقارير',
                icon: 'fas fa-chart-bar',
                url: 'reports.html',
                permission: this.canAccessPage('reports')
            }
        ];

        const employeeMenu = [
            ...baseMenu,
            {
                name: 'تسجيل الحضور',
                icon: 'fas fa-calendar-check',
                url: 'employee-attendance.html',
                permission: true
            }
        ];

        switch (this.currentUser.type) {
            case this.userTypes.ADMIN:
                return adminMenu.filter(item => item.permission);
            case this.userTypes.SUPERVISOR:
                return supervisorMenu.filter(item => item.permission);
            case this.userTypes.EMPLOYEE:
                return employeeMenu.filter(item => item.permission);
            default:
                return [];
        }
    }

    // Protect page - redirect if no access
    protectPage(pageName) {
        if (!this.isLoggedIn()) {
            window.location.href = 'login.html';
            return false;
        }

        if (!this.canAccessPage(pageName)) {
            this.showAccessDenied();
            return false;
        }

        return true;
    }

    // Show access denied message
    showAccessDenied() {
        alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
        window.location.href = this.getDashboardUrl();
    }

    // Update user profile
    updateProfile(userData) {
        if (!this.currentUser) return false;

        // Update current user data
        Object.assign(this.currentUser, userData);

        // Update in users array
        const userIndex = this.users.findIndex(u => u.id === this.currentUser.id);
        if (userIndex !== -1) {
            this.users[userIndex] = { ...this.currentUser };
        }

        // Update localStorage
        localStorage.setItem('currentUser', JSON.stringify(this.currentUser));

        return true;
    }

    // Get user statistics
    getUserStats() {
        const loginTime = localStorage.getItem('loginTime');
        const sessionDuration = loginTime ?
            Math.floor((new Date() - new Date(loginTime)) / 1000 / 60) : 0;

        return {
            sessionDuration: sessionDuration,
            lastLogin: loginTime,
            userType: this.currentUser?.type,
            permissions: this.currentUser?.permissions || []
        };
    }

    // Initialize page with user data
    initializePage() {
        if (!this.isLoggedIn()) {
            window.location.href = 'login.html';
            return;
        }

        // Update user info in header
        this.updateHeaderUserInfo();

        // Update navigation menu
        this.updateNavigationMenu();

        // Add logout functionality
        this.addLogoutHandler();
    }

    // Update header user info
    updateHeaderUserInfo() {
        const userNameElement = document.querySelector('.user-name');
        const userRoleElement = document.querySelector('.user-role');
        const userAvatarElement = document.querySelector('.user-avatar img');

        if (userNameElement) userNameElement.textContent = this.currentUser.name;
        if (userRoleElement) userRoleElement.textContent = this.getUserTypeText();
        if (userAvatarElement) userAvatarElement.src = this.currentUser.avatar;
    }

    // Update navigation menu
    updateNavigationMenu() {
        const navMenu = document.querySelector('.nav-menu');
        if (!navMenu) return;

        const menuItems = this.getNavigationMenu();
        navMenu.innerHTML = menuItems.map(item => `
            <a href="${item.url}" class="nav-link">
                <i class="${item.icon}"></i>
                <span>${item.name}</span>
            </a>
        `).join('');
    }

    // Add logout handler
    addLogoutHandler() {
        const logoutBtn = document.querySelector('.logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        }
    }

    // Get user type text in Arabic
    getUserTypeText() {
        switch (this.currentUser?.type) {
            case this.userTypes.ADMIN:
                return 'مدير النظام';
            case this.userTypes.SUPERVISOR:
                return 'مشرف';
            case this.userTypes.EMPLOYEE:
                return 'موظف';
            default:
                return 'مستخدم';
        }
    }

    // Additional methods for user management
    getAllUsers() {
        return this.users;
    }

    getUserById(id) {
        return this.users.find(user => user.id === id);
    }

    getUserByEmail(email) {
        return this.users.find(user => user.email === email);
    }

    addUser(userData) {
        try {
            // Generate new ID
            const newId = Math.max(...this.users.map(u => u.id)) + 1;

            // Create new user object
            const newUser = {
                id: newId,
                username: userData.email,
                password: userData.password,
                name: userData.name,
                email: userData.email,
                type: userData.type,
                department: userData.department || '',
                position: userData.position || '',
                avatar: userData.avatar || 'https://via.placeholder.com/100',
                permissions: this.getDefaultPermissions(userData.type),
                active: userData.active !== false,
                createdAt: userData.createdAt || new Date().toISOString().split('T')[0]
            };

            // Add to users array
            this.users.push(newUser);

            // Save to localStorage
            this.saveUsers();

            return true;
        } catch (error) {
            console.error('Error adding user:', error);
            return false;
        }
    }

    deleteUser(userId) {
        try {
            // Don't allow deleting the main admin
            if (userId === 1) {
                return false;
            }

            // Remove user from array
            this.users = this.users.filter(user => user.id !== userId);

            // Save to localStorage
            this.saveUsers();

            return true;
        } catch (error) {
            console.error('Error deleting user:', error);
            return false;
        }
    }

    getDefaultPermissions(userType) {
        switch (userType) {
            case this.userTypes.ADMIN:
                return ['all', 'add_users', 'manage_users'];
            case this.userTypes.SUPERVISOR:
                return ['view_reports', 'manage_team'];
            case this.userTypes.EMPLOYEE:
                return ['attendance_only'];
            default:
                return [];
        }
    }

    saveUsers() {
        try {
            localStorage.setItem('systemUsers', JSON.stringify(this.users));
        } catch (error) {
            console.error('Error saving users:', error);
        }
    }

    loadUsers() {
        try {
            const stored = localStorage.getItem('systemUsers');
            if (stored) {
                this.users = JSON.parse(stored);
            }
        } catch (error) {
            console.error('Error loading users:', error);
        }
    }
}

// Create global auth instance
const authSystem = new AuthSystem();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthSystem;
}
