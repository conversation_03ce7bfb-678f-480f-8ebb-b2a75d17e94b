/* Reports Page Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 3rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.logo i {
    font-size: 2rem;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: #666;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-menu:hover {
    background: #e9ecef;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    color: #666;
}

/* Main Content */
.main-content {
    padding: 2rem 0;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 16px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.page-title h1 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-title h1 i {
    color: #667eea;
}

.page-title p {
    color: #666;
    font-size: 1.1rem;
}

.page-actions {
    display: flex;
    gap: 1rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-family: inherit;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Report Filters */
.report-filters {
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    display: flex;
    gap: 2rem;
    align-items: end;
    flex-wrap: wrap;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-group label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.filter-group select,
.filter-group input {
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: inherit;
    transition: all 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.custom-date-range {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1rem;
}

.custom-date-range label {
    margin-bottom: 0;
    white-space: nowrap;
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 16px;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-icon {
    width: 70px;
    height: 70px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    color: white;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.card-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.card-content p {
    color: #666;
    font-size: 1rem;
    margin-bottom: 0.75rem;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.card-trend.positive {
    color: #28a745;
}

.card-trend.negative {
    color: #dc3545;
}

.card-trend.neutral {
    color: #6c757d;
}

/* Report Tabs */
.report-tabs {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tab-buttons {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    flex: 1;
    padding: 1.25rem 1.5rem;
    border: none;
    background: transparent;
    color: #666;
    font-family: inherit;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-btn.active {
    background: white;
    color: #667eea;
    border-bottom-color: #667eea;
}

.tab-content {
    display: none;
    padding: 2rem;
}

.tab-content.active {
    display: block;
}

/* Report Sections */
.report-section {
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f3f4;
}

.section-header h2 {
    color: #333;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-header h2 i {
    color: #667eea;
}

/* Charts Grid */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.chart-container h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.125rem;
    text-align: center;
}

.chart-container canvas {
    max-height: 300px;
}

.map-container {
    height: 300px;
    background: #e9ecef;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 1.1rem;
}

/* Data Tables */
.data-table-container {
    margin-top: 2rem;
}

.data-table-container h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.table-wrapper {
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.table-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
    padding: 1rem;
}

/* Violations Summary */
.violations-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.violation-card {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.violation-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.violation-icon.late {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.violation-icon.absence {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.violation-icon.location {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

.violation-info h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.violation-count {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    display: block;
    margin-bottom: 0.25rem;
}

.violation-info p {
    color: #666;
    font-size: 0.875rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-overlay.active {
    display: flex;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.loading-spinner p {
    color: #333;
    font-weight: 500;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.present {
    background: #d4edda;
    color: #155724;
}

.status-badge.late {
    background: #fff3cd;
    color: #856404;
}

.status-badge.absent {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.early-leave {
    background: #d1ecf1;
    color: #0c5460;
}

/* Location Badges */
.location-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.location-badge.in-office {
    background: #d4edda;
    color: #155724;
}

.location-badge.out-of-office {
    background: #f8d7da;
    color: #721c24;
}

.location-badge.partial {
    background: #fff3cd;
    color: #856404;
}

/* Interactive Elements */
.interactive-chart {
    cursor: pointer;
    transition: all 0.3s ease;
}

.interactive-chart:hover {
    transform: scale(1.02);
}

.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tooltip.show {
    opacity: 1;
}

/* Export Options */
.export-dropdown {
    position: relative;
    display: inline-block;
}

.export-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    z-index: 1000;
    display: none;
}

.export-menu.show {
    display: block;
}

.export-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid #f1f3f4;
}

.export-option:last-child {
    border-bottom: none;
}

.export-option:hover {
    background: #f8f9fa;
    color: #667eea;
}

/* Advanced Filters */
.advanced-filters {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-top: 1rem;
    display: none;
}

.advanced-filters.show {
    display: block;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.filter-row:last-child {
    margin-bottom: 0;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.quick-stat {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.quick-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.25rem;
}

.quick-stat-label {
    font-size: 0.875rem;
    color: #666;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }

    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .header-container {
        padding: 0 1rem;
        flex-direction: column;
        height: auto;
        gap: 1rem;
        padding-top: 1rem;
        padding-bottom: 1rem;
    }

    .header-right {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
    }

    .nav-menu {
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }

    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .page-actions {
        width: 100%;
        justify-content: center;
    }

    .report-filters {
        flex-direction: column;
        gap: 1rem;
    }

    .filter-group {
        width: 100%;
    }

    .custom-date-range {
        flex-direction: column;
        align-items: stretch;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .summary-card {
        flex-direction: column;
        text-align: center;
    }

    .tab-buttons {
        flex-direction: column;
    }

    .tab-btn {
        justify-content: flex-start;
        border-bottom: 1px solid #e9ecef;
        border-right: 3px solid transparent;
    }

    .tab-btn.active {
        border-bottom-color: #e9ecef;
        border-right-color: #667eea;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .chart-container {
        padding: 1rem;
    }

    .violations-summary {
        grid-template-columns: 1fr;
    }

    .violation-card {
        flex-direction: column;
        text-align: center;
    }

    .data-table {
        font-size: 0.875rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }

    .table-pagination {
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 1rem 0;
    }

    .page-title h1 {
        font-size: 1.5rem;
    }

    .page-title p {
        font-size: 1rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .card-content h3 {
        font-size: 2rem;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .section-header h2 {
        font-size: 1.25rem;
    }
}

/* Print Styles */
@media print {

    .header,
    .page-actions,
    .report-filters,
    .tab-buttons,
    .loading-overlay {
        display: none !important;
    }

    .main-content {
        padding: 0;
    }

    .container {
        max-width: none;
        padding: 0;
    }

    .page-header {
        margin-bottom: 1rem;
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .summary-cards,
    .charts-grid,
    .violations-summary {
        break-inside: avoid;
    }

    .chart-container {
        break-inside: avoid;
        margin-bottom: 1rem;
    }

    .data-table {
        font-size: 0.75rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.25rem;
    }
}