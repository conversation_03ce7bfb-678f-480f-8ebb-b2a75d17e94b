// Attendance Page JavaScript
document.addEventListener('DOMContentLoaded', function () {
    initializeAttendancePage();
    loadAttendanceData();
    setupEventListeners();
    startTimeUpdates();
});

// Sample attendance data
const attendanceData = [
    {
        id: 1,
        employeeId: 1,
        employeeName: 'أحمد محمد',
        department: 'it',
        checkInTime: '08:00',
        checkOutTime: '17:00',
        workingHours: '9:00',
        status: 'present',
        notes: 'حضور منتظم',
        avatar: 'أم',
        date: new Date().toISOString().split('T')[0]
    },
    {
        id: 2,
        employeeId: 2,
        employeeName: 'فاطمة علي',
        department: 'hr',
        checkInTime: '08:15',
        checkOutTime: '17:15',
        workingHours: '9:00',
        status: 'late',
        notes: 'تأخير 15 دقيقة',
        avatar: 'فع',
        date: new Date().toISOString().split('T')[0]
    },
    {
        id: 3,
        employeeId: 3,
        employeeName: 'محمد خالد',
        department: 'finance',
        checkInTime: '--:--',
        checkOutTime: '--:--',
        workingHours: '0:00',
        status: 'absent',
        notes: 'غياب بدون إذن',
        avatar: 'مخ',
        date: new Date().toISOString().split('T')[0]
    },
    {
        id: 4,
        employeeId: 4,
        employeeName: 'سارة أحمد',
        department: 'marketing',
        checkInTime: '07:45',
        checkOutTime: '--:--',
        workingHours: '0:00',
        status: 'on-break',
        notes: 'في استراحة الغداء',
        avatar: 'سأ',
        date: new Date().toISOString().split('T')[0]
    },
    {
        id: 5,
        employeeId: 5,
        employeeName: 'عبدالله محمود',
        department: 'sales',
        checkInTime: '08:30',
        checkOutTime: '17:30',
        workingHours: '9:00',
        status: 'late',
        notes: 'تأخير 30 دقيقة',
        avatar: 'عم',
        date: new Date().toISOString().split('T')[0]
    }
];

// Current user attendance state
let currentUserAttendance = {
    checkInTime: null,
    checkOutTime: null,
    isOnBreak: false,
    breakStartTime: null,
    totalBreakTime: 0,
    isWorkingDay: false, // Track if user is currently in working hours
    locationHistory: [] // Store location history during work
};

// GPS Configuration
const OFFICE_LOCATIONS = [
    {
        name: 'المكتب الرئيسي',
        latitude: 24.7136,
        longitude: 46.6753,
        radius: 100 // meters
    },
    {
        name: 'الفرع الثاني',
        latitude: 24.7500,
        longitude: 46.7000,
        radius: 150 // meters
    }
];

// GPS tracking variables
let userLocation = null;
let locationWatchId = null;
let workingHoursTrackingId = null;
let locationCheckInterval = null;
let outOfOfficeWarningShown = false;

let currentPage = 1;
const itemsPerPage = 10;
let filteredAttendance = [...attendanceData];

// Initialize attendance page
function initializeAttendancePage() {
    updateAttendanceStats();
    setupPagination();
    updateTodayStatus();
    setTodayDate();
    initializeGPS();
}

// Initialize GPS tracking
function initializeGPS() {
    if ('geolocation' in navigator) {
        // Show location permission banner first
        showLocationPermissionBanner();

        // Request location permission and start basic tracking
        navigator.geolocation.getCurrentPosition(
            function (position) {
                userLocation = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                    accuracy: position.coords.accuracy,
                    timestamp: new Date().toISOString()
                };

                console.log('موقع المستخدم:', userLocation);
                updateLocationStatus();
                hideLocationPermissionBanner();

                // Start basic location watching (low frequency)
                startBasicLocationTracking();
            },
            function (error) {
                console.error('خطأ في الحصول على الموقع:', error);
                handleLocationError(error);
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    } else {
        showNotification('المتصفح لا يدعم خدمات الموقع', 'error');
    }
}

// Start basic location tracking (for check-in/check-out only)
function startBasicLocationTracking() {
    if (locationWatchId) {
        navigator.geolocation.clearWatch(locationWatchId);
    }

    locationWatchId = navigator.geolocation.watchPosition(
        function (position) {
            userLocation = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                accuracy: position.coords.accuracy,
                timestamp: new Date().toISOString()
            };
            updateLocationStatus();
        },
        function (error) {
            console.error('خطأ في تتبع الموقع الأساسي:', error);
            handleLocationError(error);
        },
        {
            enableHighAccuracy: false, // Lower accuracy for basic tracking
            timeout: 30000,
            maximumAge: 300000 // 5 minutes
        }
    );
}

// Start intensive location tracking during working hours
function startWorkingHoursTracking() {
    console.log('بدء تتبع الموقع المكثف أثناء ساعات العمل');

    // Clear basic tracking
    if (locationWatchId) {
        navigator.geolocation.clearWatch(locationWatchId);
        locationWatchId = null;
    }

    // Start high-frequency tracking
    workingHoursTrackingId = navigator.geolocation.watchPosition(
        function (position) {
            const newLocation = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                accuracy: position.coords.accuracy,
                timestamp: new Date().toISOString()
            };

            userLocation = newLocation;

            // Store location in history
            currentUserAttendance.locationHistory.push(newLocation);

            // Check if still within office bounds
            checkLocationDuringWork(newLocation);

            updateLocationStatus();
        },
        function (error) {
            console.error('خطأ في تتبع الموقع أثناء العمل:', error);
            handleLocationError(error);
        },
        {
            enableHighAccuracy: true, // High accuracy during work
            timeout: 15000,
            maximumAge: 30000 // 30 seconds
        }
    );

    // Set up periodic location checks
    locationCheckInterval = setInterval(function () {
        if (currentUserAttendance.isWorkingDay && userLocation) {
            checkLocationDuringWork(userLocation);
        }
    }, 60000); // Check every minute
}

// Stop working hours tracking
function stopWorkingHoursTracking() {
    console.log('إيقاف تتبع الموقع المكثف');

    // Clear working hours tracking
    if (workingHoursTrackingId) {
        navigator.geolocation.clearWatch(workingHoursTrackingId);
        workingHoursTrackingId = null;
    }

    // Clear location check interval
    if (locationCheckInterval) {
        clearInterval(locationCheckInterval);
        locationCheckInterval = null;
    }

    // Reset warning flag
    outOfOfficeWarningShown = false;

    // Resume basic tracking
    startBasicLocationTracking();
}

// Check location during working hours
function checkLocationDuringWork(location) {
    const locationCheck = isWithinOfficeLocation();

    if (!locationCheck.isValid && !outOfOfficeWarningShown) {
        // Employee left office during work hours
        outOfOfficeWarningShown = true;

        const nearestOffice = locationCheck.nearestOffice;
        const warningMessage = nearestOffice
            ? `تحذير: لقد غادرت نطاق المكتب أثناء ساعات العمل. أقرب مكتب: ${nearestOffice.name} (${nearestOffice.distance}م)`
            : 'تحذير: لقد غادرت نطاق المكتب أثناء ساعات العمل';

        showNotification(warningMessage, 'warning');

        // Log the incident
        console.log('الموظف خارج نطاق المكتب:', {
            time: new Date().toLocaleTimeString('ar-SA'),
            location: location,
            nearestOffice: nearestOffice
        });

        // Add to location history with warning flag
        currentUserAttendance.locationHistory.push({
            ...location,
            warning: 'خارج نطاق المكتب',
            nearestOffice: nearestOffice
        });
    } else if (locationCheck.isValid && outOfOfficeWarningShown) {
        // Employee returned to office
        outOfOfficeWarningShown = false;
        showNotification(`مرحباً بعودتك إلى ${locationCheck.office}`, 'success');

        // Log the return
        console.log('الموظف عاد إلى المكتب:', {
            time: new Date().toLocaleTimeString('ar-SA'),
            office: locationCheck.office,
            distance: locationCheck.distance
        });
    }
}

// Show location permission banner
function showLocationPermissionBanner() {
    const existingBanner = document.getElementById('location-permission-banner');
    if (existingBanner) return;

    const banner = document.createElement('div');
    banner.id = 'location-permission-banner';
    banner.className = 'location-permission-banner';
    banner.innerHTML = `
        <div class="location-permission-content">
            <i class="fas fa-map-marker-alt"></i>
            <div class="location-permission-text">
                <h4>تحديد الموقع مطلوب</h4>
                <p>نحتاج إلى إذن الوصول لموقعك للتأكد من تسجيل الحضور من المكتب</p>
            </div>
        </div>
        <div class="location-permission-actions">
            <button class="location-permission-btn primary" onclick="requestLocationPermission()">
                السماح بالوصول
            </button>
            <button class="location-permission-btn" onclick="hideLocationPermissionBanner()">
                تجاهل
            </button>
        </div>
    `;

    const attendanceContent = document.querySelector('.attendance-content');
    attendanceContent.insertBefore(banner, attendanceContent.firstChild);
}

// Hide location permission banner
function hideLocationPermissionBanner() {
    const banner = document.getElementById('location-permission-banner');
    if (banner) {
        banner.remove();
    }
}

// Request location permission manually
function requestLocationPermission() {
    if ('geolocation' in navigator) {
        navigator.geolocation.getCurrentPosition(
            function (position) {
                userLocation = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                    accuracy: position.coords.accuracy
                };

                updateLocationStatus();
                hideLocationPermissionBanner();
                showNotification('تم تفعيل تتبع الموقع بنجاح!', 'success');

                // Start watching location changes
                if (locationWatchId) {
                    navigator.geolocation.clearWatch(locationWatchId);
                }

                locationWatchId = navigator.geolocation.watchPosition(
                    function (position) {
                        userLocation = {
                            latitude: position.coords.latitude,
                            longitude: position.coords.longitude,
                            accuracy: position.coords.accuracy
                        };
                        updateLocationStatus();
                    },
                    function (error) {
                        console.error('خطأ في تتبع الموقع:', error);
                        handleLocationError(error);
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 60000
                    }
                );
            },
            function (error) {
                handleLocationError(error);
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    }
}

// Calculate distance between two coordinates (Haversine formula)
function calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
        Math.cos(φ1) * Math.cos(φ2) *
        Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
}

// Check if user is within office location
function isWithinOfficeLocation() {
    if (!userLocation) {
        return { isValid: false, reason: 'لا يمكن تحديد موقعك الحالي' };
    }

    for (let office of OFFICE_LOCATIONS) {
        const distance = calculateDistance(
            userLocation.latitude,
            userLocation.longitude,
            office.latitude,
            office.longitude
        );

        if (distance <= office.radius) {
            return {
                isValid: true,
                office: office.name,
                distance: Math.round(distance)
            };
        }
    }

    return {
        isValid: false,
        reason: 'أنت خارج نطاق المكتب المسموح به',
        nearestOffice: findNearestOffice()
    };
}

// Find nearest office location
function findNearestOffice() {
    if (!userLocation) return null;

    let nearestOffice = null;
    let minDistance = Infinity;

    for (let office of OFFICE_LOCATIONS) {
        const distance = calculateDistance(
            userLocation.latitude,
            userLocation.longitude,
            office.latitude,
            office.longitude
        );

        if (distance < minDistance) {
            minDistance = distance;
            nearestOffice = {
                name: office.name,
                distance: Math.round(distance)
            };
        }
    }

    return nearestOffice;
}

// Update location status display
function updateLocationStatus() {
    const locationCheck = isWithinOfficeLocation();

    // Add location indicator to header
    let locationIndicator = document.getElementById('location-indicator');
    if (!locationIndicator) {
        locationIndicator = document.createElement('div');
        locationIndicator.id = 'location-indicator';
        locationIndicator.className = 'location-indicator';

        const headerActions = document.querySelector('.header-actions');
        if (headerActions) {
            headerActions.insertBefore(locationIndicator, headerActions.firstChild);
        }
    }

    if (locationCheck.isValid) {
        locationIndicator.innerHTML = `
            <div class="location-status valid">
                <i class="fas fa-map-marker-alt"></i>
                <span>${locationCheck.office}</span>
                <small>${locationCheck.distance}م</small>
            </div>
        `;
        locationIndicator.className = 'location-indicator valid';
    } else {
        const nearestOffice = locationCheck.nearestOffice;
        locationIndicator.innerHTML = `
            <div class="location-status invalid">
                <i class="fas fa-map-marker-alt"></i>
                <span>خارج النطاق</span>
                ${nearestOffice ? `<small>أقرب مكتب: ${nearestOffice.name} (${nearestOffice.distance}م)</small>` : ''}
            </div>
        `;
        locationIndicator.className = 'location-indicator invalid';
    }

    // Update action cards based on location status
    updateActionCardsStatus(locationCheck.isValid);
}

// Update action cards based on GPS status
function updateActionCardsStatus(isLocationValid) {
    const actionCards = document.querySelectorAll('.action-card');

    actionCards.forEach(card => {
        if (!isLocationValid && !userLocation) {
            // No GPS data available
            card.classList.add('gps-required');
            card.style.pointerEvents = 'none';
        } else {
            // GPS data available (even if outside range)
            card.classList.remove('gps-required');
            card.style.pointerEvents = 'auto';
        }
    });

    // Add accuracy indicator if location is available
    if (userLocation && userLocation.accuracy) {
        addLocationAccuracyIndicator();
    }
}

// Add location accuracy indicator
function addLocationAccuracyIndicator() {
    let accuracyIndicator = document.getElementById('location-accuracy');
    if (!accuracyIndicator) {
        accuracyIndicator = document.createElement('div');
        accuracyIndicator.id = 'location-accuracy';
        accuracyIndicator.className = 'location-accuracy';

        const locationIndicator = document.getElementById('location-indicator');
        if (locationIndicator) {
            locationIndicator.appendChild(accuracyIndicator);
        }
    }

    const accuracy = userLocation.accuracy;
    let accuracyClass = 'high';
    let accuracyText = 'دقة عالية';

    if (accuracy > 50) {
        accuracyClass = 'low';
        accuracyText = 'دقة منخفضة';
    } else if (accuracy > 20) {
        accuracyClass = 'medium';
        accuracyText = 'دقة متوسطة';
    }

    accuracyIndicator.className = `location-accuracy ${accuracyClass}`;
    accuracyIndicator.textContent = `${accuracyText} (±${Math.round(accuracy)}م)`;
}

// Handle location errors
function handleLocationError(error) {
    let errorMessage = '';

    switch (error.code) {
        case error.PERMISSION_DENIED:
            errorMessage = 'تم رفض الإذن للوصول إلى الموقع. يرجى السماح بالوصول للموقع في إعدادات المتصفح.';
            break;
        case error.POSITION_UNAVAILABLE:
            errorMessage = 'معلومات الموقع غير متاحة حالياً.';
            break;
        case error.TIMEOUT:
            errorMessage = 'انتهت مهلة طلب تحديد الموقع.';
            break;
        default:
            errorMessage = 'حدث خطأ غير معروف في تحديد الموقع.';
            break;
    }

    showNotification(errorMessage, 'error');

    // Add error indicator to header
    let locationIndicator = document.getElementById('location-indicator');
    if (!locationIndicator) {
        locationIndicator = document.createElement('div');
        locationIndicator.id = 'location-indicator';
        locationIndicator.className = 'location-indicator error';

        const headerActions = document.querySelector('.header-actions');
        headerActions.insertBefore(locationIndicator, headerActions.firstChild);
    }

    locationIndicator.innerHTML = `
        <div class="location-status error">
            <i class="fas fa-exclamation-triangle"></i>
            <span>خطأ في الموقع</span>
        </div>
    `;
    locationIndicator.className = 'location-indicator error';
}

// Start time updates
function startTimeUpdates() {
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
}

// Update current time display
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    const dateString = now.toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    document.getElementById('current-time').textContent = timeString;
    document.getElementById('current-date').textContent = dateString;
}

// Set today's date
function setTodayDate() {
    const today = new Date();
    const dateString = today.toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    document.getElementById('status-date').textContent = dateString;

    // Set date filter to today
    const dateFilter = document.getElementById('date-filter');
    dateFilter.value = today.toISOString().split('T')[0];
}

// Load and display attendance data
function loadAttendanceData() {
    const tbody = document.getElementById('attendance-tbody');
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const attendanceToShow = filteredAttendance.slice(startIndex, endIndex);

    tbody.innerHTML = '';

    attendanceToShow.forEach(attendance => {
        const row = createAttendanceRow(attendance);
        tbody.appendChild(row);
    });

    updatePaginationInfo();
}

// Create attendance table row
function createAttendanceRow(attendance) {
    const row = document.createElement('tr');

    const departmentNames = {
        'it': 'تقنية المعلومات',
        'hr': 'الموارد البشرية',
        'finance': 'المالية',
        'marketing': 'التسويق',
        'sales': 'المبيعات'
    };

    const statusNames = {
        'present': 'حاضر',
        'absent': 'غائب',
        'late': 'متأخر',
        'on-break': 'في استراحة'
    };

    row.innerHTML = `
        <td>
            <div class="employee-info">
                <div class="employee-avatar">${attendance.avatar}</div>
                <div class="employee-details">
                    <div class="employee-name">${attendance.employeeName}</div>
                    <div class="employee-department">${departmentNames[attendance.department]}</div>
                </div>
            </div>
        </td>
        <td>${departmentNames[attendance.department]}</td>
        <td>
            <span class="time-display-cell ${attendance.status === 'late' ? 'late' : ''}">${attendance.checkInTime}</span>
        </td>
        <td>
            <span class="time-display-cell">${attendance.checkOutTime}</span>
        </td>
        <td>
            <span class="working-hours">${attendance.workingHours}</span>
        </td>
        <td>
            <span class="status-badge ${attendance.status}">${statusNames[attendance.status]}</span>
        </td>
        <td>
            <span class="notes-cell" title="${attendance.notes}">${attendance.notes}</span>
        </td>
        <td>
            <div class="actions-buttons">
                <button class="action-btn-small edit" onclick="editAttendance(${attendance.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn-small manual" onclick="openModal('manual-attendance')" title="تسجيل يدوي">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="action-btn-small delete" onclick="deleteAttendance(${attendance.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </td>
    `;

    return row;
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('attendance-search');
    searchInput.addEventListener('input', function () {
        filterAttendance();
    });

    // Date filter
    const dateFilter = document.getElementById('date-filter');
    dateFilter.addEventListener('change', function () {
        filterAttendance();
    });

    // Department filter
    const departmentFilter = document.getElementById('department-filter');
    departmentFilter.addEventListener('change', function () {
        filterAttendance();
    });

    // Status filter
    const statusFilter = document.getElementById('status-filter');
    statusFilter.addEventListener('change', function () {
        filterAttendance();
    });

    // Pagination buttons
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');

    prevBtn.addEventListener('click', function () {
        if (currentPage > 1) {
            currentPage--;
            loadAttendanceData();
            updatePaginationButtons();
        }
    });

    nextBtn.addEventListener('click', function () {
        const totalPages = Math.ceil(filteredAttendance.length / itemsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            loadAttendanceData();
            updatePaginationButtons();
        }
    });

    // Manual attendance form
    const manualAttendanceForm = document.getElementById('manual-attendance-form');
    manualAttendanceForm.addEventListener('submit', function (e) {
        e.preventDefault();
        handleManualAttendance(e);
    });
}

// Filter attendance data
function filterAttendance() {
    const searchTerm = document.getElementById('attendance-search').value.toLowerCase();
    const dateFilter = document.getElementById('date-filter').value;
    const departmentFilter = document.getElementById('department-filter').value;
    const statusFilter = document.getElementById('status-filter').value;

    filteredAttendance = attendanceData.filter(attendance => {
        const matchesSearch =
            attendance.employeeName.toLowerCase().includes(searchTerm) ||
            attendance.notes.toLowerCase().includes(searchTerm);

        const matchesDate = !dateFilter || attendance.date === dateFilter;
        const matchesDepartment = !departmentFilter || attendance.department === departmentFilter;
        const matchesStatus = !statusFilter || attendance.status === statusFilter;

        return matchesSearch && matchesDate && matchesDepartment && matchesStatus;
    });

    currentPage = 1;
    loadAttendanceData();
    updatePaginationButtons();
    updateAttendanceStats();
}

// Update attendance statistics
function updateAttendanceStats() {
    const stats = {
        present: filteredAttendance.filter(att => att.status === 'present').length,
        absent: filteredAttendance.filter(att => att.status === 'absent').length,
        late: filteredAttendance.filter(att => att.status === 'late').length,
        onBreak: filteredAttendance.filter(att => att.status === 'on-break').length
    };

    const statNumbers = document.querySelectorAll('.attendance-stats .stat-number');
    if (statNumbers.length >= 4) {
        statNumbers[0].textContent = stats.present;
        statNumbers[1].textContent = stats.absent;
        statNumbers[2].textContent = stats.late;
        statNumbers[3].textContent = stats.onBreak;
    }
}

// Setup pagination
function setupPagination() {
    updatePaginationButtons();
}

// Update pagination info
function updatePaginationInfo() {
    const totalPages = Math.ceil(filteredAttendance.length / itemsPerPage);
    document.getElementById('current-page').textContent = currentPage;
    document.getElementById('total-pages').textContent = totalPages;
}

// Update pagination buttons
function updatePaginationButtons() {
    const totalPages = Math.ceil(filteredAttendance.length / itemsPerPage);
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');

    prevBtn.disabled = currentPage === 1;
    nextBtn.disabled = currentPage === totalPages || totalPages === 0;
}

// Check in function
function checkIn() {
    if (currentUserAttendance.checkInTime) {
        showNotification('لقد سجلت حضورك بالفعل اليوم!', 'warning');
        return;
    }

    // Check GPS location first
    const locationCheck = isWithinOfficeLocation();
    if (!locationCheck.isValid) {
        showNotification(`لا يمكن تسجيل الحضور: ${locationCheck.reason}`, 'error');

        // Show location confirmation dialog
        if (confirm('هل تريد تسجيل الحضور رغم كونك خارج النطاق المسموح؟ (سيتم تسجيل هذا كحضور استثنائي)')) {
            recordAttendanceWithLocation(true, 'حضور استثنائي - خارج النطاق');
        }
        return;
    }

    recordAttendanceWithLocation(true, `حضور من ${locationCheck.office}`);
}

// Record attendance with location data
function recordAttendanceWithLocation(isCheckIn, note = '') {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
    });

    const locationData = {
        latitude: userLocation?.latitude || null,
        longitude: userLocation?.longitude || null,
        accuracy: userLocation?.accuracy || null,
        timestamp: now.toISOString(),
        note: note
    };

    if (isCheckIn) {
        // Check in process
        currentUserAttendance.checkInTime = timeString;
        currentUserAttendance.checkInLocation = locationData;
        currentUserAttendance.isWorkingDay = true;
        currentUserAttendance.locationHistory = []; // Reset location history

        // Start intensive location tracking
        startWorkingHoursTracking();

        // Show working hours indicator
        updateWorkingHoursIndicator();

        showNotification(`تم تسجيل الحضور بنجاح في ${timeString}`, 'success');
        showNotification('تم تفعيل تتبع الموقع أثناء ساعات العمل', 'info');

    } else {
        // Check out process
        currentUserAttendance.checkOutTime = timeString;
        currentUserAttendance.checkOutLocation = locationData;
        currentUserAttendance.isWorkingDay = false;

        // Stop intensive location tracking
        stopWorkingHoursTracking();

        // Hide working hours indicator
        updateWorkingHoursIndicator();

        showNotification(`تم تسجيل الانصراف بنجاح في ${timeString}`, 'success');
        showNotification('تم إيقاف تتبع الموقع المكثف', 'info');

        // Generate location summary for the day
        generateLocationSummary();
    }

    updateTodayStatus();

    // Log location data for audit purposes
    console.log('بيانات الموقع:', locationData);
}

// Generate location summary for the working day
function generateLocationSummary() {
    if (currentUserAttendance.locationHistory.length === 0) {
        console.log('لا توجد بيانات موقع لهذا اليوم');
        return;
    }

    const totalLocations = currentUserAttendance.locationHistory.length;
    const outOfOfficeCount = currentUserAttendance.locationHistory.filter(loc => loc.warning).length;
    const inOfficePercentage = Math.round(((totalLocations - outOfOfficeCount) / totalLocations) * 100);

    const summary = {
        totalTrackingPoints: totalLocations,
        inOfficePoints: totalLocations - outOfOfficeCount,
        outOfOfficePoints: outOfOfficeCount,
        inOfficePercentage: inOfficePercentage,
        workingHours: calculateWorkingHours(),
        checkInLocation: currentUserAttendance.checkInLocation,
        checkOutLocation: currentUserAttendance.checkOutLocation
    };

    console.log('ملخص الموقع لهذا اليوم:', summary);

    // Show summary notification
    if (inOfficePercentage >= 90) {
        showNotification(`ممتاز! كنت في المكتب ${inOfficePercentage}% من وقت العمل`, 'success');
    } else if (inOfficePercentage >= 70) {
        showNotification(`جيد! كنت في المكتب ${inOfficePercentage}% من وقت العمل`, 'info');
    } else {
        showNotification(`تحذير: كنت خارج المكتب ${100 - inOfficePercentage}% من وقت العمل`, 'warning');
    }

    return summary;
}

// Calculate working hours
function calculateWorkingHours() {
    if (!currentUserAttendance.checkInTime || !currentUserAttendance.checkOutTime) {
        return '0:00';
    }

    const checkIn = new Date(`2024-01-01 ${currentUserAttendance.checkInTime}`);
    const checkOut = new Date(`2024-01-01 ${currentUserAttendance.checkOutTime}`);
    const workingMs = checkOut - checkIn - currentUserAttendance.totalBreakTime;
    const workingHours = Math.floor(workingMs / (1000 * 60 * 60));
    const workingMinutes = Math.floor((workingMs % (1000 * 60 * 60)) / (1000 * 60));

    return `${workingHours}:${workingMinutes.toString().padStart(2, '0')}`;
}

// Check out function
function checkOut() {
    if (!currentUserAttendance.checkInTime) {
        showNotification('يجب تسجيل الحضور أولاً!', 'error');
        return;
    }

    if (currentUserAttendance.checkOutTime) {
        showNotification('لقد سجلت انصرافك بالفعل اليوم!', 'warning');
        return;
    }

    // Check GPS location for checkout
    const locationCheck = isWithinOfficeLocation();
    if (!locationCheck.isValid) {
        showNotification(`تحذير: أنت خارج نطاق المكتب عند الانصراف`, 'warning');

        // Allow checkout but mark as unusual
        if (confirm('هل تريد تسجيل الانصراف رغم كونك خارج النطاق المسموح؟')) {
            recordAttendanceWithLocation(false, 'انصراف من خارج النطاق');
        }
        return;
    }

    recordAttendanceWithLocation(false, `انصراف من ${locationCheck.office}`);
}

// Toggle break function
function toggleBreak() {
    if (!currentUserAttendance.checkInTime) {
        showNotification('يجب تسجيل الحضور أولاً!', 'error');
        return;
    }

    if (currentUserAttendance.checkOutTime) {
        showNotification('لا يمكن أخذ استراحة بعد تسجيل الانصراف!', 'error');
        return;
    }

    const breakBtn = document.querySelector('.action-card.break .action-btn span');

    if (!currentUserAttendance.isOnBreak) {
        // Start break
        currentUserAttendance.isOnBreak = true;
        currentUserAttendance.breakStartTime = new Date();

        // Log break start location
        if (userLocation) {
            currentUserAttendance.locationHistory.push({
                ...userLocation,
                action: 'بدء الاستراحة',
                timestamp: new Date().toISOString()
            });
        }

        breakBtn.textContent = 'إنهاء الاستراحة';
        showNotification('تم بدء فترة الاستراحة', 'info');

        // Continue location tracking during break (but with different frequency)
        console.log('تتبع الموقع مستمر أثناء الاستراحة');

    } else {
        // End break
        const breakDuration = new Date() - currentUserAttendance.breakStartTime;
        currentUserAttendance.totalBreakTime += breakDuration;
        currentUserAttendance.isOnBreak = false;
        currentUserAttendance.breakStartTime = null;

        // Log break end location
        if (userLocation) {
            currentUserAttendance.locationHistory.push({
                ...userLocation,
                action: 'إنهاء الاستراحة',
                timestamp: new Date().toISOString(),
                breakDuration: Math.round(breakDuration / (1000 * 60)) // minutes
            });
        }

        breakBtn.textContent = 'بدء الاستراحة';
        showNotification('تم إنهاء فترة الاستراحة', 'info');

        // Check if employee returned to office after break
        const locationCheck = isWithinOfficeLocation();
        if (!locationCheck.isValid) {
            showNotification('تحذير: أنت خارج نطاق المكتب بعد انتهاء الاستراحة', 'warning');
        }
    }

    updateTodayStatus();
}

// Update today's status display
function updateTodayStatus() {
    document.getElementById('checkin-time').textContent = currentUserAttendance.checkInTime || '--:--';
    document.getElementById('checkout-time').textContent = currentUserAttendance.checkOutTime || '--:--';

    // Calculate working hours
    const workingHours = calculateWorkingHours();
    document.getElementById('working-hours').textContent = workingHours;

    // Calculate break time
    let totalBreakTime = currentUserAttendance.totalBreakTime;

    // Add current break time if on break
    if (currentUserAttendance.isOnBreak && currentUserAttendance.breakStartTime) {
        totalBreakTime += new Date() - currentUserAttendance.breakStartTime;
    }

    const totalBreakMinutes = Math.floor(totalBreakTime / (1000 * 60));
    const breakHours = Math.floor(totalBreakMinutes / 60);
    const breakMins = totalBreakMinutes % 60;
    document.getElementById('break-time').textContent = `${breakHours}:${breakMins.toString().padStart(2, '0')}`;

    // Update tracking status indicator
    updateTrackingStatusIndicator();
}

// Update tracking status indicator
function updateTrackingStatusIndicator() {
    let trackingIndicator = document.getElementById('tracking-status');
    if (!trackingIndicator) {
        trackingIndicator = document.createElement('div');
        trackingIndicator.id = 'tracking-status';
        trackingIndicator.className = 'tracking-status';

        const todayStatus = document.querySelector('.today-status');
        todayStatus.appendChild(trackingIndicator);
    }

    if (currentUserAttendance.isWorkingDay && !currentUserAttendance.checkOutTime) {
        // Currently working - show active tracking
        const locationCount = currentUserAttendance.locationHistory.length;
        const trackingDuration = currentUserAttendance.checkInTime ?
            Math.floor((new Date() - new Date(`2024-01-01 ${currentUserAttendance.checkInTime}`)) / (1000 * 60)) : 0;

        trackingIndicator.innerHTML = `
            <div class="tracking-status-content active">
                <div class="tracking-icon">
                    <i class="fas fa-satellite-dish"></i>
                </div>
                <div class="tracking-info">
                    <h4>تتبع الموقع نشط</h4>
                    <p>نقاط التتبع: ${locationCount} | مدة التتبع: ${trackingDuration} دقيقة</p>
                    ${currentUserAttendance.isOnBreak ? '<span class="break-indicator">في استراحة</span>' : ''}
                </div>
            </div>
        `;
        trackingIndicator.className = 'tracking-status active';

    } else if (currentUserAttendance.checkOutTime) {
        // Work day ended - show summary
        const locationCount = currentUserAttendance.locationHistory.length;
        const workingHours = calculateWorkingHours();

        trackingIndicator.innerHTML = `
            <div class="tracking-status-content completed">
                <div class="tracking-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="tracking-info">
                    <h4>انتهى التتبع لهذا اليوم</h4>
                    <p>إجمالي نقاط التتبع: ${locationCount} | ساعات العمل: ${workingHours}</p>
                </div>
            </div>
        `;
        trackingIndicator.className = 'tracking-status completed';

    } else {
        // Not started yet
        trackingIndicator.innerHTML = `
            <div class="tracking-status-content inactive">
                <div class="tracking-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="tracking-info">
                    <h4>في انتظار تسجيل الحضور</h4>
                    <p>سيتم تفعيل تتبع الموقع عند تسجيل الحضور</p>
                </div>
            </div>
        `;
        trackingIndicator.className = 'tracking-status inactive';
    }
}

// Handle manual attendance
function handleManualAttendance(e) {
    const formData = new FormData(e.target);
    const manualAttendanceData = Object.fromEntries(formData);

    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'جاري الحفظ...';
    submitBtn.disabled = true;

    // Add location data if available
    if (userLocation) {
        manualAttendanceData.latitude = userLocation.latitude;
        manualAttendanceData.longitude = userLocation.longitude;
        manualAttendanceData.locationAccuracy = userLocation.accuracy;
    }

    // Simulate API call
    setTimeout(() => {
        console.log('بيانات الحضور اليدوي:', manualAttendanceData);
        showNotification('تم حفظ بيانات الحضور بنجاح!', 'success');
        closeModal('manual-attendance');

        // Reset button
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;

        // Reset form
        e.target.reset();

        // Refresh attendance data
        loadAttendanceData();
    }, 2000);
}

// Edit attendance
function editAttendance(id) {
    const attendance = attendanceData.find(att => att.id === id);
    if (attendance) {
        alert(`تعديل حضور: ${attendance.employeeName}`);
        // Here you would typically open an edit modal
    }
}

// Delete attendance
function deleteAttendance(id) {
    const attendance = attendanceData.find(att => att.id === id);
    if (attendance && confirm(`هل أنت متأكد من حذف سجل حضور ${attendance.employeeName}؟`)) {
        const index = attendanceData.findIndex(att => att.id === id);
        if (index > -1) {
            attendanceData.splice(index, 1);
            filteredAttendance = [...attendanceData];

            loadAttendanceData();
            updateAttendanceStats();

            showNotification('تم حذف سجل الحضور بنجاح!', 'success');
        }
    }
}

// Export attendance
function exportAttendance() {
    if (filteredAttendance.length === 0) {
        showNotification('لا توجد بيانات للتصدير', 'error');
        return;
    }

    // Create CSV content
    const headers = ['الموظف', 'القسم', 'وقت الحضور', 'وقت الانصراف', 'ساعات العمل', 'الحالة', 'ملاحظات', 'التاريخ'];
    const csvContent = [
        headers.join(','),
        ...filteredAttendance.map(att => [
            att.employeeName,
            att.department,
            att.checkInTime,
            att.checkOutTime,
            att.workingHours,
            att.status,
            att.notes,
            att.date
        ].join(','))
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'attendance.csv';
    link.click();

    showNotification(`تم تصدير ${filteredAttendance.length} سجل حضور بنجاح!`, 'success');
}

// Cleanup GPS tracking when page is closed
window.addEventListener('beforeunload', function () {
    // Clear all GPS tracking
    if (locationWatchId) {
        navigator.geolocation.clearWatch(locationWatchId);
    }

    if (workingHoursTrackingId) {
        navigator.geolocation.clearWatch(workingHoursTrackingId);
    }

    if (locationCheckInterval) {
        clearInterval(locationCheckInterval);
    }

    // Save final location data if still working
    if (currentUserAttendance.isWorkingDay && !currentUserAttendance.checkOutTime) {
        console.log('تحذير: تم إغلاق الصفحة أثناء ساعات العمل');

        // Log the incident
        if (userLocation) {
            currentUserAttendance.locationHistory.push({
                ...userLocation,
                action: 'إغلاق الصفحة أثناء العمل',
                timestamp: new Date().toISOString()
            });
        }
    }
});

// Add GPS status to attendance records
function addGPSDataToAttendance(attendanceRecord) {
    if (userLocation) {
        attendanceRecord.gpsData = {
            latitude: userLocation.latitude,
            longitude: userLocation.longitude,
            accuracy: userLocation.accuracy,
            timestamp: new Date().toISOString(),
            isWithinOffice: isWithinOfficeLocation().isValid
        };
    }
    return attendanceRecord;
}

// Get current location summary for reports
function getLocationSummary() {
    if (!userLocation) {
        return 'الموقع غير متاح';
    }

    const locationCheck = isWithinOfficeLocation();
    if (locationCheck.isValid) {
        return `${locationCheck.office} (${locationCheck.distance}م)`;
    } else {
        const nearest = locationCheck.nearestOffice;
        if (nearest) {
            return `خارج النطاق - أقرب مكتب: ${nearest.name} (${nearest.distance}م)`;
        } else {
            return 'خارج النطاق';
        }
    }
}

// Show/hide working hours indicator
function updateWorkingHoursIndicator() {
    let indicator = document.getElementById('working-hours-indicator');

    if (currentUserAttendance.isWorkingDay && !currentUserAttendance.checkOutTime) {
        // Show active working hours indicator
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'working-hours-indicator';
            indicator.className = 'working-hours-indicator';
            document.body.appendChild(indicator);
        }

        const workingTime = calculateCurrentWorkingTime();
        const locationStatus = getLocationSummary();

        indicator.innerHTML = `
            <i class="fas fa-clock"></i>
            ساعات العمل: ${workingTime} | ${locationStatus}
        `;
        indicator.className = 'working-hours-indicator active';

    } else if (indicator) {
        // Hide indicator when not working
        indicator.className = 'working-hours-indicator';
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        }, 300);
    }
}

// Calculate current working time
function calculateCurrentWorkingTime() {
    if (!currentUserAttendance.checkInTime) {
        return '0:00';
    }

    const checkIn = new Date(`2024-01-01 ${currentUserAttendance.checkInTime}`);
    const now = new Date(`2024-01-01 ${new Date().toLocaleTimeString('ar-SA', { hour12: false })}`);
    let workingMs = now - checkIn - currentUserAttendance.totalBreakTime;

    // Subtract current break time if on break
    if (currentUserAttendance.isOnBreak && currentUserAttendance.breakStartTime) {
        const currentBreakTime = new Date() - currentUserAttendance.breakStartTime;
        workingMs -= currentBreakTime;
    }

    // Ensure positive value
    workingMs = Math.max(0, workingMs);

    const workingHours = Math.floor(workingMs / (1000 * 60 * 60));
    const workingMinutes = Math.floor((workingMs % (1000 * 60 * 60)) / (1000 * 60));

    return `${workingHours}:${workingMinutes.toString().padStart(2, '0')}`;
}

// Update working hours indicator every minute
setInterval(function () {
    if (currentUserAttendance.isWorkingDay && !currentUserAttendance.checkOutTime) {
        updateWorkingHoursIndicator();
        updateTodayStatus(); // Update the main status display too
    }
}, 60000); // Update every minute

console.log('صفحة الحضور والانصراف مع نظام GPS المتقدم - موقوت');
