// Attendance Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeAttendancePage();
    loadAttendanceData();
    setupEventListeners();
    startTimeUpdates();
});

// Sample attendance data
const attendanceData = [
    {
        id: 1,
        employeeId: 1,
        employeeName: 'أحمد محمد',
        department: 'it',
        checkInTime: '08:00',
        checkOutTime: '17:00',
        workingHours: '9:00',
        status: 'present',
        notes: 'حضور منتظم',
        avatar: 'أم',
        date: new Date().toISOString().split('T')[0]
    },
    {
        id: 2,
        employeeId: 2,
        employeeName: 'فاطمة علي',
        department: 'hr',
        checkInTime: '08:15',
        checkOutTime: '17:15',
        workingHours: '9:00',
        status: 'late',
        notes: 'تأخير 15 دقيقة',
        avatar: 'فع',
        date: new Date().toISOString().split('T')[0]
    },
    {
        id: 3,
        employeeId: 3,
        employeeName: 'محمد خالد',
        department: 'finance',
        checkInTime: '--:--',
        checkOutTime: '--:--',
        workingHours: '0:00',
        status: 'absent',
        notes: 'غياب بدون إذن',
        avatar: 'مخ',
        date: new Date().toISOString().split('T')[0]
    },
    {
        id: 4,
        employeeId: 4,
        employeeName: 'سارة أحمد',
        department: 'marketing',
        checkInTime: '07:45',
        checkOutTime: '--:--',
        workingHours: '0:00',
        status: 'on-break',
        notes: 'في استراحة الغداء',
        avatar: 'سأ',
        date: new Date().toISOString().split('T')[0]
    },
    {
        id: 5,
        employeeId: 5,
        employeeName: 'عبدالله محمود',
        department: 'sales',
        checkInTime: '08:30',
        checkOutTime: '17:30',
        workingHours: '9:00',
        status: 'late',
        notes: 'تأخير 30 دقيقة',
        avatar: 'عم',
        date: new Date().toISOString().split('T')[0]
    }
];

// Current user attendance state
let currentUserAttendance = {
    checkInTime: null,
    checkOutTime: null,
    isOnBreak: false,
    breakStartTime: null,
    totalBreakTime: 0
};

let currentPage = 1;
const itemsPerPage = 10;
let filteredAttendance = [...attendanceData];

// Initialize attendance page
function initializeAttendancePage() {
    updateAttendanceStats();
    setupPagination();
    updateTodayStatus();
    setTodayDate();
}

// Start time updates
function startTimeUpdates() {
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
}

// Update current time display
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    const dateString = now.toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    document.getElementById('current-time').textContent = timeString;
    document.getElementById('current-date').textContent = dateString;
}

// Set today's date
function setTodayDate() {
    const today = new Date();
    const dateString = today.toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    document.getElementById('status-date').textContent = dateString;
    
    // Set date filter to today
    const dateFilter = document.getElementById('date-filter');
    dateFilter.value = today.toISOString().split('T')[0];
}

// Load and display attendance data
function loadAttendanceData() {
    const tbody = document.getElementById('attendance-tbody');
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const attendanceToShow = filteredAttendance.slice(startIndex, endIndex);
    
    tbody.innerHTML = '';
    
    attendanceToShow.forEach(attendance => {
        const row = createAttendanceRow(attendance);
        tbody.appendChild(row);
    });
    
    updatePaginationInfo();
}

// Create attendance table row
function createAttendanceRow(attendance) {
    const row = document.createElement('tr');
    
    const departmentNames = {
        'it': 'تقنية المعلومات',
        'hr': 'الموارد البشرية',
        'finance': 'المالية',
        'marketing': 'التسويق',
        'sales': 'المبيعات'
    };
    
    const statusNames = {
        'present': 'حاضر',
        'absent': 'غائب',
        'late': 'متأخر',
        'on-break': 'في استراحة'
    };
    
    row.innerHTML = `
        <td>
            <div class="employee-info">
                <div class="employee-avatar">${attendance.avatar}</div>
                <div class="employee-details">
                    <div class="employee-name">${attendance.employeeName}</div>
                    <div class="employee-department">${departmentNames[attendance.department]}</div>
                </div>
            </div>
        </td>
        <td>${departmentNames[attendance.department]}</td>
        <td>
            <span class="time-display-cell ${attendance.status === 'late' ? 'late' : ''}">${attendance.checkInTime}</span>
        </td>
        <td>
            <span class="time-display-cell">${attendance.checkOutTime}</span>
        </td>
        <td>
            <span class="working-hours">${attendance.workingHours}</span>
        </td>
        <td>
            <span class="status-badge ${attendance.status}">${statusNames[attendance.status]}</span>
        </td>
        <td>
            <span class="notes-cell" title="${attendance.notes}">${attendance.notes}</span>
        </td>
        <td>
            <div class="actions-buttons">
                <button class="action-btn-small edit" onclick="editAttendance(${attendance.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn-small manual" onclick="openModal('manual-attendance')" title="تسجيل يدوي">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="action-btn-small delete" onclick="deleteAttendance(${attendance.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </td>
    `;
    
    return row;
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('attendance-search');
    searchInput.addEventListener('input', function() {
        filterAttendance();
    });
    
    // Date filter
    const dateFilter = document.getElementById('date-filter');
    dateFilter.addEventListener('change', function() {
        filterAttendance();
    });
    
    // Department filter
    const departmentFilter = document.getElementById('department-filter');
    departmentFilter.addEventListener('change', function() {
        filterAttendance();
    });
    
    // Status filter
    const statusFilter = document.getElementById('status-filter');
    statusFilter.addEventListener('change', function() {
        filterAttendance();
    });
    
    // Pagination buttons
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');
    
    prevBtn.addEventListener('click', function() {
        if (currentPage > 1) {
            currentPage--;
            loadAttendanceData();
            updatePaginationButtons();
        }
    });
    
    nextBtn.addEventListener('click', function() {
        const totalPages = Math.ceil(filteredAttendance.length / itemsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            loadAttendanceData();
            updatePaginationButtons();
        }
    });
    
    // Manual attendance form
    const manualAttendanceForm = document.getElementById('manual-attendance-form');
    manualAttendanceForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleManualAttendance(e);
    });
}

// Filter attendance data
function filterAttendance() {
    const searchTerm = document.getElementById('attendance-search').value.toLowerCase();
    const dateFilter = document.getElementById('date-filter').value;
    const departmentFilter = document.getElementById('department-filter').value;
    const statusFilter = document.getElementById('status-filter').value;
    
    filteredAttendance = attendanceData.filter(attendance => {
        const matchesSearch = 
            attendance.employeeName.toLowerCase().includes(searchTerm) ||
            attendance.notes.toLowerCase().includes(searchTerm);
        
        const matchesDate = !dateFilter || attendance.date === dateFilter;
        const matchesDepartment = !departmentFilter || attendance.department === departmentFilter;
        const matchesStatus = !statusFilter || attendance.status === statusFilter;
        
        return matchesSearch && matchesDate && matchesDepartment && matchesStatus;
    });
    
    currentPage = 1;
    loadAttendanceData();
    updatePaginationButtons();
    updateAttendanceStats();
}

// Update attendance statistics
function updateAttendanceStats() {
    const stats = {
        present: filteredAttendance.filter(att => att.status === 'present').length,
        absent: filteredAttendance.filter(att => att.status === 'absent').length,
        late: filteredAttendance.filter(att => att.status === 'late').length,
        onBreak: filteredAttendance.filter(att => att.status === 'on-break').length
    };
    
    const statNumbers = document.querySelectorAll('.attendance-stats .stat-number');
    if (statNumbers.length >= 4) {
        statNumbers[0].textContent = stats.present;
        statNumbers[1].textContent = stats.absent;
        statNumbers[2].textContent = stats.late;
        statNumbers[3].textContent = stats.onBreak;
    }
}

// Setup pagination
function setupPagination() {
    updatePaginationButtons();
}

// Update pagination info
function updatePaginationInfo() {
    const totalPages = Math.ceil(filteredAttendance.length / itemsPerPage);
    document.getElementById('current-page').textContent = currentPage;
    document.getElementById('total-pages').textContent = totalPages;
}

// Update pagination buttons
function updatePaginationButtons() {
    const totalPages = Math.ceil(filteredAttendance.length / itemsPerPage);
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');
    
    prevBtn.disabled = currentPage === 1;
    nextBtn.disabled = currentPage === totalPages || totalPages === 0;
}

// Check in function
function checkIn() {
    if (currentUserAttendance.checkInTime) {
        showNotification('لقد سجلت حضورك بالفعل اليوم!', 'warning');
        return;
    }
    
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
    });
    
    currentUserAttendance.checkInTime = timeString;
    updateTodayStatus();
    
    showNotification(`تم تسجيل الحضور بنجاح في ${timeString}`, 'success');
}

// Check out function
function checkOut() {
    if (!currentUserAttendance.checkInTime) {
        showNotification('يجب تسجيل الحضور أولاً!', 'error');
        return;
    }
    
    if (currentUserAttendance.checkOutTime) {
        showNotification('لقد سجلت انصرافك بالفعل اليوم!', 'warning');
        return;
    }
    
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
    });
    
    currentUserAttendance.checkOutTime = timeString;
    updateTodayStatus();
    
    showNotification(`تم تسجيل الانصراف بنجاح في ${timeString}`, 'success');
}

// Toggle break function
function toggleBreak() {
    if (!currentUserAttendance.checkInTime) {
        showNotification('يجب تسجيل الحضور أولاً!', 'error');
        return;
    }
    
    const breakBtn = document.querySelector('.action-card.break .action-btn span');
    
    if (!currentUserAttendance.isOnBreak) {
        // Start break
        currentUserAttendance.isOnBreak = true;
        currentUserAttendance.breakStartTime = new Date();
        breakBtn.textContent = 'إنهاء الاستراحة';
        showNotification('تم بدء فترة الاستراحة', 'info');
    } else {
        // End break
        const breakDuration = new Date() - currentUserAttendance.breakStartTime;
        currentUserAttendance.totalBreakTime += breakDuration;
        currentUserAttendance.isOnBreak = false;
        currentUserAttendance.breakStartTime = null;
        breakBtn.textContent = 'بدء الاستراحة';
        showNotification('تم إنهاء فترة الاستراحة', 'info');
    }
    
    updateTodayStatus();
}

// Update today's status display
function updateTodayStatus() {
    document.getElementById('checkin-time').textContent = currentUserAttendance.checkInTime || '--:--';
    document.getElementById('checkout-time').textContent = currentUserAttendance.checkOutTime || '--:--';
    
    // Calculate working hours
    if (currentUserAttendance.checkInTime && currentUserAttendance.checkOutTime) {
        const checkIn = new Date(`2024-01-01 ${currentUserAttendance.checkInTime}`);
        const checkOut = new Date(`2024-01-01 ${currentUserAttendance.checkOutTime}`);
        const workingMs = checkOut - checkIn - currentUserAttendance.totalBreakTime;
        const workingHours = Math.floor(workingMs / (1000 * 60 * 60));
        const workingMinutes = Math.floor((workingMs % (1000 * 60 * 60)) / (1000 * 60));
        document.getElementById('working-hours').textContent = `${workingHours}:${workingMinutes.toString().padStart(2, '0')}`;
    } else {
        document.getElementById('working-hours').textContent = '0:00';
    }
    
    // Calculate break time
    const totalBreakMinutes = Math.floor(currentUserAttendance.totalBreakTime / (1000 * 60));
    const breakHours = Math.floor(totalBreakMinutes / 60);
    const breakMins = totalBreakMinutes % 60;
    document.getElementById('break-time').textContent = `${breakHours}:${breakMins.toString().padStart(2, '0')}`;
}

// Handle manual attendance
function handleManualAttendance(e) {
    const formData = new FormData(e.target);
    const attendanceData = Object.fromEntries(formData);
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'جاري الحفظ...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        showNotification('تم حفظ بيانات الحضور بنجاح!', 'success');
        closeModal('manual-attendance');
        
        // Reset button
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
        
        // Reset form
        e.target.reset();
    }, 2000);
}

// Edit attendance
function editAttendance(id) {
    const attendance = attendanceData.find(att => att.id === id);
    if (attendance) {
        alert(`تعديل حضور: ${attendance.employeeName}`);
        // Here you would typically open an edit modal
    }
}

// Delete attendance
function deleteAttendance(id) {
    const attendance = attendanceData.find(att => att.id === id);
    if (attendance && confirm(`هل أنت متأكد من حذف سجل حضور ${attendance.employeeName}؟`)) {
        const index = attendanceData.findIndex(att => att.id === id);
        if (index > -1) {
            attendanceData.splice(index, 1);
            filteredAttendance = [...attendanceData];
            
            loadAttendanceData();
            updateAttendanceStats();
            
            showNotification('تم حذف سجل الحضور بنجاح!', 'success');
        }
    }
}

// Export attendance
function exportAttendance() {
    if (filteredAttendance.length === 0) {
        showNotification('لا توجد بيانات للتصدير', 'error');
        return;
    }
    
    // Create CSV content
    const headers = ['الموظف', 'القسم', 'وقت الحضور', 'وقت الانصراف', 'ساعات العمل', 'الحالة', 'ملاحظات', 'التاريخ'];
    const csvContent = [
        headers.join(','),
        ...filteredAttendance.map(att => [
            att.employeeName,
            att.department,
            att.checkInTime,
            att.checkOutTime,
            att.workingHours,
            att.status,
            att.notes,
            att.date
        ].join(','))
    ].join('\n');
    
    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'attendance.csv';
    link.click();
    
    showNotification(`تم تصدير ${filteredAttendance.length} سجل حضور بنجاح!`, 'success');
}

console.log('صفحة الحضور والانصراف - موقوت');
