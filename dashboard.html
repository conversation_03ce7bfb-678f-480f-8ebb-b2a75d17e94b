<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم | KHALAiFAT Live Location</title>
    <link rel="icon" type="image/x-icon" href="https://cdn.mawqoot.com/static/images/favicon.ico">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="dashboard-style.css">
</head>

<body>
    <!-- Authentication Check Script -->
    <script src="auth-system.js"></script>
    <script>
        // Check authentication before page loads
        if (!authSystem.isLoggedIn()) {
            window.location.href = 'login.html';
        } else {
            // Redirect to appropriate dashboard based on user type
            const currentUser = authSystem.getCurrentUser();
            if (currentUser.type === authSystem.userTypes.ADMIN) {
                window.location.href = 'admin-dashboard.html';
            } else if (currentUser.type === authSystem.userTypes.SUPERVISOR) {
                window.location.href = 'supervisor-dashboard.html';
            } else if (currentUser.type === authSystem.userTypes.EMPLOYEE) {
                window.location.href = 'employee-dashboard.html';
            }
        }
    </script>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-map-marker-alt" style="color: #667eea; font-size: 2rem; margin-bottom: 0.5rem;"></i>
                <span style="font-size: 1.5rem; font-weight: 700; color: #333;">KHALAiFAT</span>
            </div>
            <button class="sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul>
                <li class="nav-item active">
                    <a href="dashboard.html" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="employees.html" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>الموظفين</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="attendance.html" class="nav-link">
                        <i class="fas fa-clock"></i>
                        <span>الحضور والانصراف</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="leaves.html" class="nav-link">
                        <i class="fas fa-calendar-times"></i>
                        <span>الإجازات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="violations.html" class="nav-link">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>المخالفات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="reports.html" class="nav-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>التقارير</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="payroll.html" class="nav-link">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>الرواتب</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="settings.html" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name">أحمد محمد</span>
                    <span class="user-role">مدير النظام</span>
                </div>
            </div>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        <!-- Top Header -->
        <header class="top-header">
            <div class="header-left">
                <button class="mobile-menu-btn" id="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>لوحة التحكم</h1>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <div class="date-time">
                        <span id="current-date"></span>
                        <span id="current-time"></span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3>إجمالي الموظفين</h3>
                        <span class="stat-number">156</span>
                        <span class="stat-change positive">+5 هذا الشهر</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3>الحاضرين اليوم</h3>
                        <span class="stat-number">142</span>
                        <span class="stat-change positive">91% معدل الحضور</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-times"></i>
                    </div>
                    <div class="stat-info">
                        <h3>الغائبين اليوم</h3>
                        <span class="stat-number">14</span>
                        <span class="stat-change negative">9% معدل الغياب</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3>المتأخرين اليوم</h3>
                        <span class="stat-number">8</span>
                        <span class="stat-change neutral">5% معدل التأخير</span>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>إحصائيات الحضور الأسبوعية</h3>
                        <div class="chart-controls">
                            <select id="week-selector">
                                <option value="current">الأسبوع الحالي</option>
                                <option value="last">الأسبوع الماضي</option>
                                <option value="month">هذا الشهر</option>
                            </select>
                        </div>
                    </div>
                    <canvas id="attendanceChart"></canvas>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <h3>توزيع الأقسام</h3>
                    </div>
                    <canvas id="departmentChart"></canvas>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="activities-section">
                <div class="section-header">
                    <h3>الأنشطة الأخيرة</h3>
                    <a href="reports.html" class="view-all-btn">عرض الكل</a>
                </div>

                <div class="activities-list">
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="activity-content">
                            <p><strong>سارة أحمد</strong> تم إضافتها كموظفة جديدة</p>
                            <span class="activity-time">منذ 10 دقائق</span>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="activity-content">
                            <p><strong>محمد علي</strong> سجل حضوره</p>
                            <span class="activity-time">منذ 25 دقيقة</span>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-calendar-times"></i>
                        </div>
                        <div class="activity-content">
                            <p><strong>فاطمة خالد</strong> طلبت إجازة مرضية</p>
                            <span class="activity-time">منذ ساعة</span>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="activity-content">
                            <p><strong>أحمد محمود</strong> تأخر عن العمل</p>
                            <span class="activity-time">منذ ساعتين</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h3>إجراءات سريعة</h3>
                <div class="actions-grid">
                    <button class="action-btn" onclick="openModal('add-employee')">
                        <i class="fas fa-user-plus"></i>
                        <span>إضافة موظف</span>
                    </button>
                    <button class="action-btn" onclick="window.location.href='attendance.html'">
                        <i class="fas fa-clock"></i>
                        <span>تسجيل حضور</span>
                    </button>
                    <button class="action-btn" onclick="window.location.href='reports.html'">
                        <i class="fas fa-file-alt"></i>
                        <span>إنشاء تقرير</span>
                    </button>
                    <button class="action-btn" onclick="openModal('send-notification')">
                        <i class="fas fa-bell"></i>
                        <span>إرسال إشعار</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div id="add-employee-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة موظف جديد</h3>
                <span class="close-modal" onclick="closeModal('add-employee')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="add-employee-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label>الاسم الأول</label>
                            <input type="text" required>
                        </div>
                        <div class="form-group">
                            <label>الاسم الأخير</label>
                            <input type="text" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>البريد الإلكتروني</label>
                            <input type="email" required>
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف</label>
                            <input type="tel" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>القسم</label>
                            <select required>
                                <option value="">اختر القسم</option>
                                <option value="it">تقنية المعلومات</option>
                                <option value="hr">الموارد البشرية</option>
                                <option value="finance">المالية</option>
                                <option value="marketing">التسويق</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>المنصب</label>
                            <input type="text" required>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal('add-employee')">إلغاء</button>
                        <button type="submit" class="btn-primary">إضافة الموظف</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Custom JavaScript -->
    <script src="dashboard-script.js"></script>
</body>

</html>