// Login Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const loginForm = document.getElementById('login-form');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const loginBtn = document.querySelector('.login-btn');
    const feedbackModal = document.getElementById('feedback-modal');
    const feedbackForm = document.getElementById('feedback-form');

    // Form validation
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    function validatePassword(password) {
        return password.length >= 6;
    }

    function showError(input, message) {
        const formGroup = input.parentElement;
        formGroup.classList.add('error');
        formGroup.classList.remove('success');
        
        let errorElement = formGroup.querySelector('.error-message');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'error-message';
            formGroup.appendChild(errorElement);
        }
        errorElement.textContent = message;
    }

    function showSuccess(input) {
        const formGroup = input.parentElement;
        formGroup.classList.add('success');
        formGroup.classList.remove('error');
        
        const errorElement = formGroup.querySelector('.error-message');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    function clearValidation(input) {
        const formGroup = input.parentElement;
        formGroup.classList.remove('error', 'success');
        
        const errorElement = formGroup.querySelector('.error-message');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    // Real-time validation
    emailInput.addEventListener('blur', function() {
        const email = this.value.trim();
        if (email === '') {
            showError(this, 'البريد الإلكتروني مطلوب');
        } else if (!validateEmail(email)) {
            showError(this, 'يرجى إدخال بريد إلكتروني صحيح');
        } else {
            showSuccess(this);
        }
    });

    emailInput.addEventListener('input', function() {
        if (this.parentElement.classList.contains('error')) {
            clearValidation(this);
        }
    });

    passwordInput.addEventListener('blur', function() {
        const password = this.value;
        if (password === '') {
            showError(this, 'كلمة المرور مطلوبة');
        } else if (!validatePassword(password)) {
            showError(this, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        } else {
            showSuccess(this);
        }
    });

    passwordInput.addEventListener('input', function() {
        if (this.parentElement.classList.contains('error')) {
            clearValidation(this);
        }
    });

    // Login form submission
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = emailInput.value.trim();
        const password = passwordInput.value;
        const remember = document.getElementById('remember').checked;
        
        let isValid = true;

        // Validate email
        if (email === '') {
            showError(emailInput, 'البريد الإلكتروني مطلوب');
            isValid = false;
        } else if (!validateEmail(email)) {
            showError(emailInput, 'يرجى إدخال بريد إلكتروني صحيح');
            isValid = false;
        } else {
            showSuccess(emailInput);
        }

        // Validate password
        if (password === '') {
            showError(passwordInput, 'كلمة المرور مطلوبة');
            isValid = false;
        } else if (!validatePassword(password)) {
            showError(passwordInput, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            isValid = false;
        } else {
            showSuccess(passwordInput);
        }

        if (isValid) {
            // Show loading state
            loginBtn.classList.add('loading');
            loginBtn.disabled = true;

            // Simulate login process
            setTimeout(() => {
                // Here you would normally send the data to your server
                console.log('Login attempt:', { email, password, remember });
                
                // For demo purposes, simulate successful login
                if (email === '<EMAIL>' && password === 'password') {
                    // Successful login
                    showNotification('تم تسجيل الدخول بنجاح!', 'success');
                    setTimeout(() => {
                        // Redirect to dashboard
                        window.location.href = 'dashboard.html';
                    }, 1500);
                } else {
                    // Failed login
                    showNotification('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'error');
                    loginBtn.classList.remove('loading');
                    loginBtn.disabled = false;
                }
            }, 2000);
        }
    });

    // Password toggle functionality
    window.togglePassword = function() {
        const passwordInput = document.getElementById('password');
        const toggleBtn = document.querySelector('.toggle-password i');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleBtn.classList.remove('fa-eye');
            toggleBtn.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleBtn.classList.remove('fa-eye-slash');
            toggleBtn.classList.add('fa-eye');
        }
    };

    // Feedback modal functions
    window.openFeedbackModal = function() {
        feedbackModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    };

    window.closeFeedbackModal = function() {
        feedbackModal.style.display = 'none';
        document.body.style.overflow = 'auto';
        feedbackForm.reset();
    };

    // Close modal when clicking outside
    feedbackModal.addEventListener('click', function(e) {
        if (e.target === feedbackModal) {
            closeFeedbackModal();
        }
    });

    // Close modal with escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && feedbackModal.style.display === 'block') {
            closeFeedbackModal();
        }
    });

    // Feedback form submission
    feedbackForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = document.getElementById('feedback-email').value;
        const message = document.getElementById('feedback-message').value;
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'جاري الإرسال...';
        submitBtn.disabled = true;
        
        // Simulate sending feedback
        setTimeout(() => {
            console.log('Feedback sent:', { email, message });
            showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.', 'success');
            closeFeedbackModal();
            
            // Reset button
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }, 1500);
    });

    // Notification system
    function showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => notification.remove());
        
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Add notification styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
            color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
            border-radius: 8px;
            padding: 1rem;
            max-width: 400px;
            z-index: 10000;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            animation: slideInRight 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 1rem;
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    // Add notification animations to head
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        
        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .notification-close {
            background: none;
            border: none;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        
        .notification-close:hover {
            opacity: 1;
        }
    `;
    document.head.appendChild(style);

    // Remember me functionality
    const rememberCheckbox = document.getElementById('remember');
    
    // Load saved credentials if remember me was checked
    if (localStorage.getItem('rememberMe') === 'true') {
        const savedEmail = localStorage.getItem('savedEmail');
        if (savedEmail) {
            emailInput.value = savedEmail;
            rememberCheckbox.checked = true;
        }
    }

    // Save/remove credentials based on remember me checkbox
    rememberCheckbox.addEventListener('change', function() {
        if (!this.checked) {
            localStorage.removeItem('rememberMe');
            localStorage.removeItem('savedEmail');
        }
    });

    // Save email when form is submitted successfully
    function saveCredentials(email) {
        if (rememberCheckbox.checked) {
            localStorage.setItem('rememberMe', 'true');
            localStorage.setItem('savedEmail', email);
        }
    }

    console.log('صفحة تسجيل الدخول - موقوت');
});
