// Simple Requests System for KHALAiFAT Live Location
// Manages employee requests with approval workflow

class SimpleRequestsSystem {
    constructor() {
        this.requests = [];
        this.currentFilter = 'all';
        this.loadRequests();
        this.initializeSystem();
    }

    initializeSystem() {
        this.loadCurrentUserInfo();
        this.setupSidebar();
        this.loadRequestsTable();
        this.updateTabCounts();
        this.setupEventListeners();
    }

    loadCurrentUserInfo() {
        const currentUser = authSystem.getCurrentUser();
        if (currentUser) {
            const userNameElement = document.getElementById('current-user-name');
            const userRoleElement = document.getElementById('current-user-role');

            if (userNameElement) {
                userNameElement.textContent = currentUser.name;
            }

            if (userRoleElement) {
                const roleText = this.getUserRoleText(currentUser.type);
                userRoleElement.textContent = roleText;
            }
        }
    }

    setupSidebar() {
        const currentUser = authSystem.getCurrentUser();
        if (!currentUser) return;

        // Hide user management link for non-admin users
        const userManagementLink = document.getElementById('user-management-link');
        if (userManagementLink && !authSystem.canManageUsers()) {
            userManagementLink.style.display = 'none';
        }

        // Update dashboard link based on user type
        const dashboardLink = document.getElementById('dashboard-link');
        if (dashboardLink) {
            dashboardLink.href = authSystem.getDashboardUrl();
        }
    }

    getUserRoleText(type) {
        const roles = {
            'admin': 'مدير النظام',
            'supervisor': 'مشرف',
            'employee': 'موظف'
        };
        return roles[type] || 'مستخدم';
    }

    loadRequests() {
        try {
            const stored = localStorage.getItem('simpleRequests');
            if (stored) {
                this.requests = JSON.parse(stored);
            } else {
                // Initialize with sample data
                this.requests = this.getSampleRequests();
                this.saveRequests();
            }
        } catch (error) {
            console.error('Error loading requests:', error);
            this.requests = this.getSampleRequests();
        }
    }

    getSampleRequests() {
        const currentUser = authSystem.getCurrentUser();
        const currentUserId = currentUser ? currentUser.id : 1;

        return [
            {
                id: 1,
                type: 'leave',
                title: 'طلب إجازة سنوية',
                description: 'أرغب في الحصول على إجازة سنوية لمدة أسبوع للسفر مع العائلة',
                requesterId: currentUserId,
                requesterName: currentUser ? currentUser.name : 'محمد جاسم',
                submissionDate: '2024-01-15',
                startDate: '2024-02-01',
                endDate: '2024-02-07',
                status: 'pending',
                priority: 'medium',
                attachments: [],
                approvedBy: null,
                approvalDate: null,
                comments: []
            },
            {
                id: 2,
                type: 'equipment',
                title: 'طلب جهاز كمبيوتر محمول',
                description: 'أحتاج إلى جهاز كمبيوتر محمول جديد للعمل من المنزل',
                requesterId: currentUserId,
                requesterName: currentUser ? currentUser.name : 'محمد جاسم',
                submissionDate: '2024-01-10',
                startDate: null,
                endDate: null,
                status: 'approved',
                priority: 'high',
                attachments: [],
                approvedBy: 'مدير تقنية المعلومات',
                approvalDate: '2024-01-12',
                comments: [
                    {
                        author: 'مدير تقنية المعلومات',
                        date: '2024-01-12',
                        text: 'تم الموافقة على الطلب. سيتم توفير الجهاز خلال أسبوع.'
                    }
                ]
            },
            {
                id: 3,
                type: 'overtime',
                title: 'طلب عمل إضافي',
                description: 'أرغب في العمل ساعات إضافية لإنجاز المشروع في الموعد المحدد',
                requesterId: currentUserId,
                requesterName: currentUser ? currentUser.name : 'محمد جاسم',
                submissionDate: '2024-01-08',
                startDate: '2024-01-15',
                endDate: '2024-01-20',
                status: 'rejected',
                priority: 'low',
                attachments: [],
                approvedBy: 'مدير الموارد البشرية',
                approvalDate: '2024-01-09',
                comments: [
                    {
                        author: 'مدير الموارد البشرية',
                        date: '2024-01-09',
                        text: 'عذراً، لا يمكن الموافقة على العمل الإضافي في الوقت الحالي بسبب قيود الميزانية.'
                    }
                ]
            }
        ];
    }

    saveRequests() {
        try {
            localStorage.setItem('simpleRequests', JSON.stringify(this.requests));
        } catch (error) {
            console.error('Error saving requests:', error);
        }
    }

    loadRequestsTable() {
        const tbody = document.getElementById('requests-tbody');
        if (!tbody) return;

        const filteredRequests = this.getFilteredRequests();

        if (filteredRequests.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" style="text-align: center; padding: 2rem; color: #666;">
                        <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>
                        لا توجد طلبات ${this.getFilterText()}
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = filteredRequests.map(request => `
            <tr>
                <td>#${request.id.toString().padStart(4, '0')}</td>
                <td>
                    <div class="request-type">
                        <i class="fas ${this.getRequestTypeIcon(request.type)}"></i>
                        ${this.getRequestTypeText(request.type)}
                    </div>
                </td>
                <td>${request.requesterName}</td>
                <td>${this.formatDate(request.submissionDate)}</td>
                <td>
                    <span class="status-badge ${request.status}">
                        <i class="fas ${this.getStatusIcon(request.status)}"></i>
                        ${this.getStatusText(request.status)}
                    </span>
                </td>
                <td>
                    <span class="priority-badge ${request.priority}">
                        ${this.getPriorityText(request.priority)}
                    </span>
                </td>
                <td class="action-buttons-cell">
                    <button class="btn-icon" onclick="requestsSystem.viewRequest(${request.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${this.canEditRequest(request) ? `
                        <button class="btn-icon" onclick="requestsSystem.editRequest(${request.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                    ` : ''}
                    ${this.canDeleteRequest(request) ? `
                        <button class="btn-icon danger" onclick="requestsSystem.deleteRequest(${request.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </td>
            </tr>
        `).join('');
    }

    getFilteredRequests() {
        let filtered = this.requests;

        // Apply status filter
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(request => request.status === this.currentFilter);
        }

        // Apply search filter
        const searchTerm = document.getElementById('search-input')?.value?.toLowerCase();
        if (searchTerm) {
            filtered = filtered.filter(request =>
                request.title.toLowerCase().includes(searchTerm) ||
                request.description.toLowerCase().includes(searchTerm) ||
                request.requesterName.toLowerCase().includes(searchTerm) ||
                this.getRequestTypeText(request.type).toLowerCase().includes(searchTerm)
            );
        }

        // Sort by submission date (newest first)
        return filtered.sort((a, b) => new Date(b.submissionDate) - new Date(a.submissionDate));
    }

    getFilterText() {
        const filterTexts = {
            'all': '',
            'pending': 'قيد الانتظار',
            'approved': 'موافق عليها',
            'rejected': 'مرفوضة'
        };
        return filterTexts[this.currentFilter] || '';
    }

    updateTabCounts() {
        const counts = {
            all: this.requests.length,
            pending: this.requests.filter(r => r.status === 'pending').length,
            approved: this.requests.filter(r => r.status === 'approved').length,
            rejected: this.requests.filter(r => r.status === 'rejected').length
        };

        Object.keys(counts).forEach(key => {
            const element = document.getElementById(`${key}-count`);
            if (element) {
                element.textContent = counts[key];
            }
        });
    }

    getRequestTypeIcon(type) {
        const icons = {
            'leave': 'fa-calendar-times',
            'permission': 'fa-user-clock',
            'overtime': 'fa-clock',
            'equipment': 'fa-laptop',
            'maintenance': 'fa-tools',
            'training': 'fa-graduation-cap',
            'other': 'fa-question-circle'
        };
        return icons[type] || 'fa-file-alt';
    }

    getRequestTypeText(type) {
        const types = {
            'leave': 'طلب إجازة',
            'permission': 'طلب إذن',
            'overtime': 'عمل إضافي',
            'equipment': 'طلب معدات',
            'maintenance': 'طلب صيانة',
            'training': 'طلب تدريب',
            'other': 'أخرى'
        };
        return types[type] || 'غير محدد';
    }

    getStatusIcon(status) {
        const icons = {
            'pending': 'fa-clock',
            'approved': 'fa-check-circle',
            'rejected': 'fa-times-circle'
        };
        return icons[status] || 'fa-question-circle';
    }

    getStatusText(status) {
        const statuses = {
            'pending': 'قيد الانتظار',
            'approved': 'موافق عليه',
            'rejected': 'مرفوض'
        };
        return statuses[status] || 'غير محدد';
    }

    getPriorityText(priority) {
        const priorities = {
            'low': 'منخفضة',
            'medium': 'متوسطة',
            'high': 'عالية',
            'urgent': 'عاجلة'
        };
        return priorities[priority] || 'غير محدد';
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    canEditRequest(request) {
        const currentUser = authSystem.getCurrentUser();
        if (!currentUser) return false;

        // Only requester can edit pending requests
        return request.requesterId === currentUser.id && request.status === 'pending';
    }

    canDeleteRequest(request) {
        const currentUser = authSystem.getCurrentUser();
        if (!currentUser) return false;

        // Admin can delete any request, requester can delete pending requests
        return currentUser.type === 'admin' ||
            (request.requesterId === currentUser.id && request.status === 'pending');
    }

    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('input', () => {
                this.loadRequestsTable();
            });
        }
    }

    viewRequest(requestId) {
        const request = this.requests.find(r => r.id === requestId);
        if (!request) {
            showNotification('لم يتم العثور على الطلب', 'error');
            return;
        }

        const modal = document.getElementById('request-details-modal');
        const content = document.getElementById('request-details-content');
        const actions = document.getElementById('request-actions');

        // Build request details HTML
        content.innerHTML = `
            <div class="request-details">
                <div class="detail-row">
                    <label>رقم الطلب:</label>
                    <span>#${request.id.toString().padStart(4, '0')}</span>
                </div>
                <div class="detail-row">
                    <label>نوع الطلب:</label>
                    <span>
                        <i class="fas ${this.getRequestTypeIcon(request.type)}"></i>
                        ${this.getRequestTypeText(request.type)}
                    </span>
                </div>
                <div class="detail-row">
                    <label>العنوان:</label>
                    <span>${request.title}</span>
                </div>
                <div class="detail-row">
                    <label>مقدم الطلب:</label>
                    <span>${request.requesterName}</span>
                </div>
                <div class="detail-row">
                    <label>تاريخ التقديم:</label>
                    <span>${this.formatDate(request.submissionDate)}</span>
                </div>
                ${request.startDate ? `
                    <div class="detail-row">
                        <label>تاريخ البداية:</label>
                        <span>${this.formatDate(request.startDate)}</span>
                    </div>
                ` : ''}
                ${request.endDate ? `
                    <div class="detail-row">
                        <label>تاريخ النهاية:</label>
                        <span>${this.formatDate(request.endDate)}</span>
                    </div>
                ` : ''}
                <div class="detail-row">
                    <label>الحالة:</label>
                    <span class="status-badge ${request.status}">
                        <i class="fas ${this.getStatusIcon(request.status)}"></i>
                        ${this.getStatusText(request.status)}
                    </span>
                </div>
                <div class="detail-row">
                    <label>الأولوية:</label>
                    <span class="priority-badge ${request.priority}">
                        ${this.getPriorityText(request.priority)}
                    </span>
                </div>
                <div class="detail-row full-width">
                    <label>الوصف:</label>
                    <div class="description-content">${request.description}</div>
                </div>
                ${request.approvedBy ? `
                    <div class="detail-row">
                        <label>تمت الموافقة بواسطة:</label>
                        <span>${request.approvedBy}</span>
                    </div>
                    <div class="detail-row">
                        <label>تاريخ الموافقة:</label>
                        <span>${this.formatDate(request.approvalDate)}</span>
                    </div>
                ` : ''}
                ${request.comments && request.comments.length > 0 ? `
                    <div class="detail-row full-width">
                        <label>التعليقات:</label>
                        <div class="comments-section">
                            ${request.comments.map(comment => `
                                <div class="comment">
                                    <div class="comment-header">
                                        <strong>${comment.author}</strong>
                                        <span class="comment-date">${this.formatDate(comment.date)}</span>
                                    </div>
                                    <div class="comment-text">${comment.text}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;

        // Build action buttons based on user permissions
        const currentUser = authSystem.getCurrentUser();
        let actionButtons = '';

        if (request.status === 'pending' && this.canApproveRequest()) {
            actionButtons += `
                <button type="button" class="btn btn-success" onclick="requestsSystem.approveRequest(${request.id})">
                    <i class="fas fa-check"></i>
                    موافقة
                </button>
                <button type="button" class="btn btn-danger" onclick="requestsSystem.rejectRequest(${request.id})">
                    <i class="fas fa-times"></i>
                    رفض
                </button>
            `;
        }

        actionButtons += `
            <button type="button" class="btn btn-secondary" onclick="closeRequestDetailsModal()">
                <i class="fas fa-times"></i>
                إغلاق
            </button>
        `;

        actions.innerHTML = actionButtons;
        modal.style.display = 'block';
    }

    canApproveRequest() {
        const currentUser = authSystem.getCurrentUser();
        return currentUser && (currentUser.type === 'admin' || currentUser.type === 'supervisor');
    }

    approveRequest(requestId) {
        const request = this.requests.find(r => r.id === requestId);
        if (!request) return;

        const currentUser = authSystem.getCurrentUser();
        request.status = 'approved';
        request.approvedBy = currentUser.name;
        request.approvalDate = new Date().toISOString().split('T')[0];

        this.saveRequests();
        this.loadRequestsTable();
        this.updateTabCounts();
        closeRequestDetailsModal();
        showNotification('تم الموافقة على الطلب بنجاح', 'success');
    }

    rejectRequest(requestId) {
        const request = this.requests.find(r => r.id === requestId);
        if (!request) return;

        const currentUser = authSystem.getCurrentUser();
        request.status = 'rejected';
        request.approvedBy = currentUser.name;
        request.approvalDate = new Date().toISOString().split('T')[0];

        this.saveRequests();
        this.loadRequestsTable();
        this.updateTabCounts();
        closeRequestDetailsModal();
        showNotification('تم رفض الطلب', 'success');
    }

    editRequest(requestId) {
        // This will be implemented later
        showNotification('سيتم تطوير وظيفة التعديل قريباً', 'info');
    }

    deleteRequest(requestId) {
        if (!confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
            return;
        }

        const index = this.requests.findIndex(r => r.id === requestId);
        if (index !== -1) {
            this.requests.splice(index, 1);
            this.saveRequests();
            this.loadRequestsTable();
            this.updateTabCounts();
            showNotification('تم حذف الطلب بنجاح', 'success');
        }
    }
}

// Initialize the system when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    window.requestsSystem = new SimpleRequestsSystem();
});

// Global functions for HTML onclick events
function filterRequests(status) {
    // Update active tab
    document.querySelectorAll('.filter-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    event.target.closest('.filter-tab').classList.add('active');

    // Update filter and reload table
    requestsSystem.currentFilter = status;
    requestsSystem.loadRequestsTable();
}

function searchRequests() {
    requestsSystem.loadRequestsTable();
}

function openNewRequestModal() {
    document.getElementById('new-request-modal').style.display = 'block';
}

function closeNewRequestModal() {
    document.getElementById('new-request-modal').style.display = 'none';
    document.getElementById('new-request-form').reset();
}

function closeRequestDetailsModal() {
    document.getElementById('request-details-modal').style.display = 'none';
}

function submitNewRequest() {
    const form = document.getElementById('new-request-form');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const currentUser = authSystem.getCurrentUser();
    if (!currentUser) {
        showNotification('خطأ في تحديد المستخدم الحالي', 'error');
        return;
    }

    // Collect form data
    const requestData = {
        id: Date.now(), // Simple ID generation
        type: document.getElementById('request-type').value,
        title: document.getElementById('request-title').value,
        description: document.getElementById('request-description').value,
        requesterId: currentUser.id,
        requesterName: currentUser.name,
        submissionDate: new Date().toISOString().split('T')[0],
        startDate: document.getElementById('start-date').value || null,
        endDate: document.getElementById('end-date').value || null,
        status: 'pending',
        priority: document.getElementById('priority').value,
        attachments: [], // File handling will be added later
        approvedBy: null,
        approvalDate: null,
        comments: []
    };

    // Add to requests array
    requestsSystem.requests.unshift(requestData);
    requestsSystem.saveRequests();

    // Update UI
    requestsSystem.loadRequestsTable();
    requestsSystem.updateTabCounts();

    // Close modal and show success message
    closeNewRequestModal();
    showNotification('تم إرسال الطلب بنجاح', 'success');
}

function exportRequests() {
    const filteredRequests = requestsSystem.getFilteredRequests();

    if (filteredRequests.length === 0) {
        showNotification('لا توجد طلبات للتصدير', 'error');
        return;
    }

    // Prepare CSV data
    const headers = ['رقم الطلب', 'نوع الطلب', 'العنوان', 'مقدم الطلب', 'تاريخ التقديم', 'الحالة', 'الأولوية'];
    const csvData = [headers];

    filteredRequests.forEach(request => {
        csvData.push([
            `#${request.id.toString().padStart(4, '0')}`,
            requestsSystem.getRequestTypeText(request.type),
            request.title,
            request.requesterName,
            requestsSystem.formatDate(request.submissionDate),
            requestsSystem.getStatusText(request.status),
            requestsSystem.getPriorityText(request.priority)
        ]);
    });

    // Convert to CSV string
    const csvString = csvData.map(row =>
        row.map(cell => `"${cell}"`).join(',')
    ).join('\n');

    // Create and download file
    const blob = new Blob(['\ufeff' + csvString], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `الطلبات_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification('تم تصدير الطلبات بنجاح', 'success');
}

// Header functions
function toggleUserMenu() {
    const dropdown = document.getElementById('user-dropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

function toggleNotifications() {
    showNotification('سيتم تطوير نظام الإشعارات قريباً', 'info');
}

function viewProfile() {
    showNotification('سيتم فتح صفحة الملف الشخصي قريباً', 'info');
}

// Notification system
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;

    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        border-radius: 8px;
        padding: 1rem;
        max-width: 400px;
        z-index: 10000;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        animation: slideInRight 0.3s ease;
    `;

    // Add to page
    document.body.appendChild(notification);

    // Remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Close dropdown when clicking outside
document.addEventListener('click', function (e) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('user-dropdown');

    if (dropdown && userMenu && !userMenu.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});
